#!/usr/bin/env python3
"""
Homara App Build and Run Script
Simplified build system to replace complex .bat files
"""

import os
import sys
import subprocess
import argparse
import platform
from pathlib import Path

class HomaraAppBuilder:
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.target_dir = self.project_root / "target"
        self.jar_name = "homara-app-1.0-SNAPSHOT-jar-with-dependencies.jar"
        self.jar_path = self.target_dir / self.jar_name
        
    def check_java(self):
        """Check if Java is installed and get version"""
        try:
            result = subprocess.run(["java", "-version"], 
                                  capture_output=True, text=True, check=True)
            print("✓ Java is installed")
            print(f"  Version info: {result.stderr.split()[2]}")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("✗ Java is not installed or not in PATH")
            print("  Please install Java 17 or later")
            return False
    
    def check_maven(self):
        """Check if <PERSON><PERSON> is installed"""
        try:
            result = subprocess.run(["mvn", "-version"], 
                                  capture_output=True, text=True, check=True)
            print("✓ Maven is installed")
            return True
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("✗ Maven is not installed or not in PATH")
            print("  Please install Apache Maven")
            return False
    
    def clean(self):
        """Clean the project"""
        print("🧹 Cleaning project...")
        try:
            subprocess.run(["mvn", "clean"], cwd=self.project_root, check=True)
            print("✓ Project cleaned successfully")
            return True
        except subprocess.CalledProcessError as e:
            print(f"✗ Clean failed: {e}")
            return False
    
    def build(self):
        """Build the project"""
        print("🔨 Building project...")
        try:
            # Use Maven to compile and package
            subprocess.run(["mvn", "clean", "package"], 
                          cwd=self.project_root, check=True)
            
            if self.jar_path.exists():
                print(f"✓ Build successful! JAR created: {self.jar_path}")
                return True
            else:
                print("✗ Build completed but JAR file not found")
                return False
                
        except subprocess.CalledProcessError as e:
            print(f"✗ Build failed: {e}")
            return False
    
    def run(self):
        """Run the application"""
        if not self.jar_path.exists():
            print("✗ JAR file not found. Please build the project first.")
            return False
        
        print("🚀 Starting Homara App...")
        try:
            # Java arguments for JavaFX and other compatibility
            java_args = [
                "java",
                "--add-opens", "java.base/java.lang=ALL-UNNAMED",
                "--add-opens", "java.base/java.util=ALL-UNNAMED", 
                "--add-opens", "java.base/java.nio=ALL-UNNAMED",
                "--add-opens", "java.base/sun.nio.ch=ALL-UNNAMED",
                "-jar", str(self.jar_path)
            ]
            
            # Run the application
            subprocess.run(java_args, cwd=self.project_root, check=True)
            
        except subprocess.CalledProcessError as e:
            print(f"✗ Failed to run application: {e}")
            return False
        except KeyboardInterrupt:
            print("\n🛑 Application stopped by user")
            return True
    
    def build_and_run(self):
        """Build and run the application"""
        if self.build():
            print("\n" + "="*50)
            self.run()
        else:
            print("Build failed, cannot run application")
    
    def info(self):
        """Show project information"""
        print("📋 Homara App Information")
        print("="*40)
        print(f"Project Root: {self.project_root}")
        print(f"Target Directory: {self.target_dir}")
        print(f"JAR File: {self.jar_name}")
        print(f"JAR Exists: {'✓' if self.jar_path.exists() else '✗'}")
        print(f"Platform: {platform.system()} {platform.release()}")
        print()
        
        # Check dependencies
        print("Dependencies:")
        self.check_java()
        self.check_maven()

def main():
    parser = argparse.ArgumentParser(description="Homara App Build and Run Tool")
    parser.add_argument("action", nargs="?", default="build-run",
                       choices=["build", "run", "clean", "build-run", "info"],
                       help="Action to perform (default: build-run)")
    
    args = parser.parse_args()
    builder = HomaraAppBuilder()
    
    print("🏠 Homara App Build Tool")
    print("="*30)
    
    if args.action == "info":
        builder.info()
    elif args.action == "clean":
        builder.clean()
    elif args.action == "build":
        builder.build()
    elif args.action == "run":
        builder.run()
    elif args.action == "build-run":
        builder.build_and_run()
    
    print("\n✨ Done!")

if __name__ == "__main__":
    main()
