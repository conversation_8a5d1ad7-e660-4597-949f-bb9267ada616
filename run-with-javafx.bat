@echo off
echo Running Homara App with JavaFX...

REM Set the path to your JavaFX SDK
set JAVAFX_PATH=C:\path\to\javafx-sdk-17.0.2\lib

REM Run the application with JavaFX modules
java --module-path %JAVAFX_PATH% --add-modules javafx.controls,javafx.fxml,javafx.web --add-opens java.base/java.lang=ALL-UNNAMED --add-opens java.base/java.util=ALL-UNNAMED --add-opens java.base/java.nio=ALL-UNNAMED --add-opens java.base/sun.nio.ch=ALL-UNNAMED -jar target/homara-app-1.0-SNAPSHOT-jar-with-dependencies.jar
