# Homara App - Simplified Version

A simplified JavaFX application with Firebase authentication.

## Features

- **Firebase Authentication**: <PERSON><PERSON> using email and password via Firebase REST API
- **Username Extraction**: Automatically creates username from email (everything before @)
- **Modern UI**: JavaFX-based interface with multiple tabs:
  - Home Dashboard
  - Payments
  - Profile
  - Settings
- **Simplified Build**: Python script replaces complex batch files

## Requirements

- Java 17 or later
- Apache Maven
- Python 3.6+ (for build script)
- Internet connection (for Firebase authentication)

## Quick Start

### Using Python Build Script (Recommended)

1. **Build and Run** (one command):
   ```bash
   python build_and_run.py
   ```

2. **Other commands**:
   ```bash
   python build_and_run.py build    # Build only
   python build_and_run.py run      # Run only (if already built)
   python build_and_run.py clean    # Clean project
   python build_and_run.py info     # Show project info
   ```

### Using Maven Directly

1. **Build**:
   ```bash
   mvn clean package
   ```

2. **Run**:
   ```bash
   java --add-opens java.base/java.lang=ALL-UNNAMED \
        --add-opens java.base/java.util=ALL-UNNAMED \
        --add-opens java.base/java.nio=ALL-UNNAMED \
        --add-opens java.base/sun.nio.ch=ALL-UNNAMED \
        -jar target/homara-app-1.0-SNAPSHOT-jar-with-dependencies.jar
   ```

## Firebase Configuration

The app uses Firebase Authentication with a pre-configured API key. For production use, you should:

1. Create your own Firebase project
2. Enable Authentication with Email/Password
3. Update the API key in `FirebaseClientSideAuth.java`

## Project Structure

```
src/main/java/com/homara/app/
├── Main.java                    # Application entry point
├── Launcher.java               # JAR launcher
├── firebase/
│   ├── FirebaseInitializer.java    # Firebase setup
│   └── FirebaseClientSideAuth.java # Authentication service
└── ui/
    ├── AuthenticationWindow.java   # Login window
    └── MainWindow.java            # Main application window
```

## Changes from Original

This simplified version removes:
- MongoDB integration
- Spring Boot framework
- API clients and services
- Cache system
- Complex build scripts

And keeps:
- Firebase authentication (client-side only)
- JavaFX UI components
- Username extraction from email
- Core application functionality

## Troubleshooting

1. **Build fails**: Ensure Java 17+ and Maven are installed
2. **JavaFX issues**: The build script includes necessary JavaFX arguments
3. **Firebase errors**: Check internet connection and Firebase configuration
4. **Python script issues**: Ensure Python 3.6+ is installed

## Development

To modify the application:

1. Edit source files in `src/main/java/com/homara/app/`
2. Use `python build_and_run.py` to test changes
3. The main UI components are in the `ui` package
4. Firebase authentication logic is in `firebase` package
