# Migration from Java to Node.js

## What Was Converted

### Java Files → Node.js Files
- `src/main/java/com/homara/app/Main.java` → `src/main.js` (Electron main process)
- `src/main/java/com/homara/app/ui/AuthenticationWindow.java` → `src/auth.html`
- `src/main/java/com/homara/app/ui/MainWindow.java` → `src/main.html`
- `src/main/java/com/homara/app/firebase/FirebaseClientSideAuth.java` → `src/firebase-auth.js`

### Build System
- `pom.xml` (Maven) → `package.json` (npm)
- `build_and_run.py` (Java/Maven) → `run.py` (Node.js/npm)
- Complex .bat files → Simple npm scripts

### Dependencies
- JavaFX → Electron (for desktop UI)
- Firebase Admin SDK → Firebase REST API (same as before)
- Maven dependencies → npm packages

## Key Improvements

1. **No Maven Required**: Uses npm instead
2. **No JavaFX Issues**: Uses web technologies
3. **Easier Integration**: Same tech stack as web frontend/backend
4. **Better UI**: Modern CSS, responsive design
5. **Cross-Platform**: Electron handles platform differences
6. **Faster Development**: Hot reload, familiar web dev tools

## Files You Can Remove (if desired)

The following Java files are no longer needed:
- `src/main/java/` (entire directory)
- `pom.xml`
- `dependency-reduced-pom.xml`
- `build.bat`
- `run.bat`
- `run-with-javafx.bat`
- `run-with-maven.bat`
- `test-mongodb.bat`
- `build_and_run.py` (old Java version)
- `simple_run.py` (old Java version)
- `target/` (Maven build directory)

## What to Keep

- `package.json` - Node.js project configuration
- `src/` - New Node.js/Electron source files
- `assets/` - Application assets
- `run.py` - New Node.js runner script
- `README.md` - Updated documentation
- This file (`MIGRATION_NOTES.md`)

## Firebase Configuration

The Firebase API key and authentication logic remain the same, just converted from Java to JavaScript.

## Running the New Version

```bash
python3 run.py        # Install deps and run
# OR
npm install && npm start
```
