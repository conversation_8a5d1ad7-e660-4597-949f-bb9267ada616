package com.homara.app.mongodb.service;

import com.homara.app.mongodb.model.User;
import com.homara.app.mongodb.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.Optional;

@Service
public class AuthenticationService {
    
    private final UserRepository userRepository;
    
    @Autowired
    public AuthenticationService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }
    
    /**
     * Authenticate a user with email and password
     * @param email User's email
     * @param password User's password
     * @return User object if authentication is successful, null otherwise
     */
    public User authenticateUser(String email, String password) {
        Optional<User> userOptional = userRepository.findByEmail(email);
        
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            // In a real application, you would use a password encoder to check the password
            if (user.getPassword().equals(password)) {
                return user;
            }
        }
        
        return null;
    }
    
    /**
     * Create a new user account
     * @param username Username
     * @param email Email
     * @param password Password
     * @param name Full name
     * @return User object if creation is successful, null otherwise
     */
    public User createUser(String username, String email, String password, String name) {
        // Check if email or username already exists
        if (userRepository.existsByEmail(email) || userRepository.existsByUsername(username)) {
            return null;
        }
        
        User user = new User(username, email, password, name);
        return userRepository.save(user);
    }
    
    /**
     * Get user by ID
     * @param id User ID
     * @return User object if found, null otherwise
     */
    public User getUserById(String id) {
        return userRepository.findById(id).orElse(null);
    }
}
