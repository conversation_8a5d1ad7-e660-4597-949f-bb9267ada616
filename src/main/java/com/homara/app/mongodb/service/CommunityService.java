package com.homara.app.mongodb.service;

import com.homara.app.mongodb.model.Community;
import com.homara.app.mongodb.model.User;
import com.homara.app.mongodb.repository.UserRepository;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
public class CommunityService {
    
    private final UserRepository userRepository;
    private final MongoTemplate mongoTemplate;
    
    @Autowired
    public CommunityService(UserRepository userRepository, MongoTemplate mongoTemplate) {
        this.userRepository = userRepository;
        this.mongoTemplate = mongoTemplate;
    }
    
    /**
     * Get all communities for a user
     * @param userId User ID
     * @return List of communities
     */
    public List<Community> getUserCommunities(String userId) {
        Optional<User> userOptional = userRepository.findById(userId);
        return userOptional.map(User::getCommunities).orElse(null);
    }
    
    /**
     * Add a community to a user
     * @param userId User ID
     * @param community Community to add
     * @return Updated user
     */
    public User addCommunityToUser(String userId, Community community) {
        Optional<User> userOptional = userRepository.findById(userId);
        
        if (userOptional.isPresent()) {
            User user = userOptional.get();
            user.addCommunity(community);
            return userRepository.save(user);
        }
        
        return null;
    }
    
    /**
     * Find communities by name (partial match)
     * @param name Community name to search for
     * @return List of matching communities
     */
    public List<Community> findCommunitiesByName(String name) {
        Query query = new Query();
        query.addCriteria(Criteria.where("name").regex(name, "i"));
        return mongoTemplate.find(query, Community.class);
    }
}
