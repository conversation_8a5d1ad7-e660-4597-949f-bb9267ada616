package com.homara.app.mongodb.service;

import com.homara.app.mongodb.model.User;

/**
 * A simplified authentication service that uses the StandaloneApp
 * for authentication
 */
public class SimpleAuthService {

    /**
     * Authenticate a user with email and password
     * @param email User's email
     * @param password User's password (not used in this simplified version)
     * @return User object if found, null otherwise
     */
    public User authenticateUser(String email, String password) {
        // Use the StandaloneApp's authentication method
        return com.homara.app.standalone.StandaloneApp.authenticateUser(email, password);
    }
}
