package com.homara.app.mongodb;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.mongodb.config.AbstractMongoClientConfiguration;
import org.springframework.data.mongodb.core.MongoTemplate;

@Configuration
public class MongoDBConfig extends AbstractMongoClientConfiguration {

    // Update this with your actual MongoDB connection string
    // For local MongoDB: mongodb://localhost:27017/homara
    // For MongoDB Atlas: mongodb+srv://username:<EMAIL>/homara
    private static final String CONNECTION_STRING = "mongodb://localhost:27017/homara";
    private static final String DATABASE_NAME = "homara";

    @Override
    protected String getDatabaseName() {
        return DATABASE_NAME;
    }

    @Override
    @Bean
    public MongoClient mongoClient() {
        return MongoClients.create(CONNECTION_STRING);
    }

    @Bean
    public MongoTemplate mongoTemplate() {
        return new MongoTemplate(mongoClient(), getDatabaseName());
    }
}
