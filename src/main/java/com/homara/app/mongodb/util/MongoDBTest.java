package com.homara.app.mongodb.util;

import org.bson.Document;

/**
 * Test class for MongoDB connection and operations
 * Run this class to test your MongoDB connection and view data
 */
public class MongoDBTest {

    public static void main(String[] args) {
        // Test connection
        System.out.println("Testing MongoDB connection...");
        boolean connected = MongoDBUtil.testConnection();

        if (connected) {
            System.out.println("Successfully connected to MongoDB!");

            // Get all users
            System.out.println("\nFetching all users:");
            Iterable<Document> users = MongoDBUtil.getAllDocuments("users");

            if (users != null) {
                for (Document user : users) {
                    System.out.println(user.toJson());
                }
            } else {
                System.out.println("No users found or collection doesn't exist.");
            }

            // Create a test user if needed
            boolean createTestUser = false; // Set to true to create a test user

            if (createTestUser) {
                System.out.println("\nCreating a test user...");
                Document testUser = new Document()
                        .append("CommunityID", "<EMAIL>")
                        .append("PointID", "testuser001")
                        .append("PointPosition", "X, Y, Z")
                        .append("email", "<EMAIL>")
                        .append("username", "testuser")
                        .append("password", "password123")
                        .append("name", "Test User");

                boolean inserted = MongoDBUtil.insertDocument("users", testUser);

                if (inserted) {
                    System.out.println("Test user created successfully!");
                } else {
                    System.out.println("Failed to create test user.");
                }
            }
        } else {
            System.out.println("Failed to connect to MongoDB. Please check your connection string and ensure MongoDB is running.");
        }
    }
}
