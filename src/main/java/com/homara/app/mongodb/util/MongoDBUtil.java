package com.homara.app.mongodb.util;

import com.mongodb.client.MongoClient;
import com.mongodb.client.MongoClients;
import com.mongodb.client.MongoCollection;
import com.mongodb.client.MongoDatabase;
import org.bson.Document;

/**
 * Utility class for MongoDB operations
 */
public class MongoDBUtil {
    
    private static final String DEFAULT_CONNECTION_STRING = "mongodb://localhost:27017";
    private static final String DEFAULT_DATABASE_NAME = "homara";
    
    /**
     * Test the MongoDB connection
     * @param connectionString MongoDB connection string
     * @return true if connection is successful, false otherwise
     */
    public static boolean testConnection(String connectionString) {
        try (MongoClient mongoClient = MongoClients.create(connectionString)) {
            // List all database names to verify connection
            mongoClient.listDatabaseNames().first();
            return true;
        } catch (Exception e) {
            System.err.println("Failed to connect to MongoDB: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Test the MongoDB connection with default connection string
     * @return true if connection is successful, false otherwise
     */
    public static boolean testConnection() {
        return testConnection(DEFAULT_CONNECTION_STRING);
    }
    
    /**
     * Get all documents from a collection
     * @param connectionString MongoDB connection string
     * @param databaseName Database name
     * @param collectionName Collection name
     * @return Iterable of Documents
     */
    public static Iterable<Document> getAllDocuments(String connectionString, String databaseName, String collectionName) {
        try (MongoClient mongoClient = MongoClients.create(connectionString)) {
            MongoDatabase database = mongoClient.getDatabase(databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            return collection.find();
        } catch (Exception e) {
            System.err.println("Failed to get documents: " + e.getMessage());
            return null;
        }
    }
    
    /**
     * Get all documents from a collection with default connection string and database name
     * @param collectionName Collection name
     * @return Iterable of Documents
     */
    public static Iterable<Document> getAllDocuments(String collectionName) {
        return getAllDocuments(DEFAULT_CONNECTION_STRING, DEFAULT_DATABASE_NAME, collectionName);
    }
    
    /**
     * Insert a document into a collection
     * @param connectionString MongoDB connection string
     * @param databaseName Database name
     * @param collectionName Collection name
     * @param document Document to insert
     * @return true if insertion is successful, false otherwise
     */
    public static boolean insertDocument(String connectionString, String databaseName, String collectionName, Document document) {
        try (MongoClient mongoClient = MongoClients.create(connectionString)) {
            MongoDatabase database = mongoClient.getDatabase(databaseName);
            MongoCollection<Document> collection = database.getCollection(collectionName);
            collection.insertOne(document);
            return true;
        } catch (Exception e) {
            System.err.println("Failed to insert document: " + e.getMessage());
            return false;
        }
    }
    
    /**
     * Insert a document into a collection with default connection string and database name
     * @param collectionName Collection name
     * @param document Document to insert
     * @return true if insertion is successful, false otherwise
     */
    public static boolean insertDocument(String collectionName, Document document) {
        return insertDocument(DEFAULT_CONNECTION_STRING, DEFAULT_DATABASE_NAME, collectionName, document);
    }
}
