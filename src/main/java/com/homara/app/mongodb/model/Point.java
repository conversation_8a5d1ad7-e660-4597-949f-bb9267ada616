package com.homara.app.mongodb.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * MongoDB document model for Point.
 */
@Document(collection = "points")
public class Point {
    
    @Id
    private String id;
    private String pointId;      // The PointID field from MongoDB
    private String communityId;  // The CommunityID field from MongoDB
    private String userId;       // Reference to the user who owns this point
    private String position;     // The PointPosition field from MongoDB
    private String name;         // Name of the point
    
    /**
     * Default constructor.
     */
    public Point() {
    }
    
    /**
     * Creates a new Point with the specified properties.
     *
     * @param pointId the point ID
     * @param communityId the community ID
     * @param userId the user ID
     * @param position the point position
     * @param name the point name
     */
    public Point(String pointId, String communityId, String userId, String position, String name) {
        this.pointId = pointId;
        this.communityId = communityId;
        this.userId = userId;
        this.position = position;
        this.name = name;
    }
    
    /**
     * Gets the point's database ID.
     *
     * @return the point's database ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Sets the point's database ID.
     *
     * @param id the point's database ID
     */
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * Gets the point ID.
     *
     * @return the point ID
     */
    public String getPointId() {
        return pointId;
    }
    
    /**
     * Sets the point ID.
     *
     * @param pointId the point ID
     */
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    /**
     * Gets the community ID.
     *
     * @return the community ID
     */
    public String getCommunityId() {
        return communityId;
    }
    
    /**
     * Sets the community ID.
     *
     * @param communityId the community ID
     */
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    
    /**
     * Gets the user ID.
     *
     * @return the user ID
     */
    public String getUserId() {
        return userId;
    }
    
    /**
     * Sets the user ID.
     *
     * @param userId the user ID
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    /**
     * Gets the point position.
     *
     * @return the point position
     */
    public String getPosition() {
        return position;
    }
    
    /**
     * Sets the point position.
     *
     * @param position the point position
     */
    public void setPosition(String position) {
        this.position = position;
    }
    
    /**
     * Gets the point name.
     *
     * @return the point name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Sets the point name.
     *
     * @param name the point name
     */
    public void setName(String name) {
        this.name = name;
    }
}
