package com.homara.app.mongodb.model;

import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.ArrayList;
import java.util.List;

@Document(collection = "users")
public class User {

    @Id
    private String id;
    private String CommunityID;  // Based on your MongoDB structure
    private String PointID;      // Based on your MongoDB structure
    private String PointPosition; // Based on your MongoDB structure

    // Keep these fields for authentication and display purposes
    private String username;
    private String email;
    private String password;
    private String name;
    private List<Community> communities = new ArrayList<>();

    public User() {
    }

    public User(String username, String email, String password, String name) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.name = name;
    }

    public User(String communityID, String pointID, String pointPosition) {
        this.CommunityID = communityID;
        this.PointID = pointID;
        this.PointPosition = pointPosition;
    }

    public User(String username, String email, String password, String name,
                String communityID, String pointID, String pointPosition) {
        this.username = username;
        this.email = email;
        this.password = password;
        this.name = name;
        this.CommunityID = communityID;
        this.PointID = pointID;
        this.PointPosition = pointPosition;
    }

    public String getId() {
        return id;
    }

    public void setId(String id) {
        this.id = id;
    }

    public String getUsername() {
        return username;
    }

    public void setUsername(String username) {
        this.username = username;
    }

    public String getEmail() {
        return email;
    }

    public void setEmail(String email) {
        this.email = email;
    }

    public String getPassword() {
        return password;
    }

    public void setPassword(String password) {
        this.password = password;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public List<Community> getCommunities() {
        return communities;
    }

    public void setCommunities(List<Community> communities) {
        this.communities = communities;
    }

    public void addCommunity(Community community) {
        this.communities.add(community);
    }

    public String getCommunityID() {
        return CommunityID;
    }

    public void setCommunityID(String communityID) {
        this.CommunityID = communityID;
    }

    public String getPointID() {
        return PointID;
    }

    public void setPointID(String pointID) {
        this.PointID = pointID;
    }

    public String getPointPosition() {
        return PointPosition;
    }

    public void setPointPosition(String pointPosition) {
        this.PointPosition = pointPosition;
    }
}
