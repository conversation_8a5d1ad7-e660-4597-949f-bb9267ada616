package com.homara.app;

import com.homara.app.firebase.FirebaseInitializer;
import com.homara.app.spring.SpringBootConfig;
import com.homara.app.ui.AuthenticationWindow;
import javafx.application.Application;
import javafx.application.Platform;
import javafx.scene.control.Alert;
import javafx.stage.Stage;

public class Main extends Application {

    @Override
    public void start(Stage primaryStage) {
        // Initialize Firebase
        boolean firebaseInitialized = FirebaseInitializer.initialize();

        if (!firebaseInitialized) {
            // Show error alert if Firebase initialization fails
            Alert alert = new Alert(Alert.AlertType.ERROR);
            alert.setTitle("Firebase Initialization Error");
            alert.setHeaderText("Failed to initialize Firebase");
            alert.setContentText("The application could not connect to Firebase. Please check your internet connection and try again.");
            alert.showAndWait();

            // Exit the application
            Platform.exit();
            return;
        }

        System.out.println("Firebase initialized successfully, opening authentication window");

        // Create and show the authentication window
        AuthenticationWindow authWindow = new AuthenticationWindow();
        authWindow.show();
    }

    public static void main(String[] args) {
        // Start Spring Boot in a separate thread
        Thread springThread = new Thread(() -> {
            SpringBootConfig.startSpringApplication(args);
        });
        springThread.setDaemon(true);
        springThread.start();

        // Start JavaFX UI
        launch(args);
    }
}
