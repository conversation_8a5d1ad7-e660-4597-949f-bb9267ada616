package com.homara.app.firebase;

import com.google.api.core.ApiFuture;
import com.google.cloud.firestore.*;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.UserRecord;

import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ExecutionException;

public class AuthService {
    private final Firestore firestore;
    private final CollectionReference usersCollection;
    private final FirebaseClientSideAuth clientAuth;
    private static final String USERS_COLLECTION = "users";
    private static final String USERNAME_FIELD = "username";
    private static final String USER_ID_FIELD = "userId";

    public AuthService() {
        this.firestore = FirebaseInitializer.getFirestore();
        this.usersCollection = firestore.collection(USERS_COLLECTION);
        this.clientAuth = new FirebaseClientSideAuth();
    }

    /**
     * Create a new user account with username and password
     */
    public boolean createUser(String username, String password) {
        try {
            // Check if username already exists
            if (isUsernameTaken(username)) {
                System.out.println("Username already taken: " + username);
                return false;
            }

            // Create a unique email using the username (for Firebase Auth)
            String email = username + "@homara-app.firebaseapp.com";

            // Create user in Firebase Authentication
            UserRecord.CreateRequest request = new UserRecord.CreateRequest()
                    .setEmail(email)
                    .setPassword(password)
                    .setDisplayName(username);

            UserRecord userRecord = FirebaseAuth.getInstance().createUser(request);
            String userId = userRecord.getUid();

            // Store username to userId mapping in Firestore
            Map<String, Object> userData = new HashMap<>();
            userData.put(USERNAME_FIELD, username);
            userData.put(USER_ID_FIELD, userId);
            userData.put("email", email);
            userData.put("createdAt", FieldValue.serverTimestamp());

            // Add user to Firestore
            usersCollection.document(userId).set(userData);

            System.out.println("Successfully created user: " + username);
            return true;
        } catch (FirebaseAuthException | ExecutionException | InterruptedException e) {
            System.err.println("Error creating user: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    /**
     * Result class to hold authentication result with error information
     */
    public static class AuthResult {
        private final String userId;
        private final String errorCode;
        private final String errorMessage;

        private AuthResult(String userId, String errorCode, String errorMessage) {
            this.userId = userId;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static AuthResult success(String userId) {
            return new AuthResult(userId, null, null);
        }

        public static AuthResult error(String errorCode, String errorMessage) {
            return new AuthResult(null, errorCode, errorMessage);
        }

        public boolean isSuccess() {
            return userId != null;
        }

        public String getUserId() {
            return userId;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }

    /**
     * Authenticate a user with email and password
     *
     * This method uses the Firebase client-side SDK to authenticate the user
     * with email and password directly against Firebase Authentication.
     *
     * @return AuthResult object containing either the user ID or error information
     */
    public AuthResult authenticateUserWithResult(String email, String password) {
        try {
            System.out.println("==================================================");
            System.out.println("Authenticating user with email: " + email);
            System.out.println("Password length: " + (password != null ? password.length() : 0));
            System.out.println("==================================================");

            // Use the client-side auth service to authenticate the user
            String userId = clientAuth.authenticateUser(email, password);

            System.out.println("==================================================");
            System.out.println("User authenticated successfully: " + userId);
            System.out.println("==================================================");

            try {
                // Check if the user exists in Firestore
                DocumentReference userDocRef = usersCollection.document(userId);
                DocumentSnapshot userDoc = userDocRef.get().get();

                if (userDoc.exists()) {
                    System.out.println("User found in Firestore");
                    // Print user data for debugging
                    Map<String, Object> userData = userDoc.getData();
                    if (userData != null) {
                        System.out.println("User data in Firestore:");
                        for (Map.Entry<String, Object> entry : userData.entrySet()) {
                            System.out.println("  " + entry.getKey() + ": " + entry.getValue());
                        }
                    }
                } else {
                    System.out.println("User authenticated but not found in Firestore");
                    // The web app will handle creating the Firestore document
                }
            } catch (Exception e) {
                System.out.println("Error checking Firestore: " + e.getMessage());
                e.printStackTrace();
                // Continue anyway since authentication was successful
            }

            return AuthResult.success(userId);
        } catch (AuthenticationException e) {
            System.out.println("==================================================");
            System.out.println("Authentication failed with code: " + e.getErrorCode());
            System.out.println("Error message: " + e.getMessage());
            System.out.println("==================================================");
            return AuthResult.error(e.getErrorCode(), e.getMessage());
        } catch (Exception e) {
            System.err.println("Error authenticating user: " + e.getMessage());
            e.printStackTrace();
            return AuthResult.error("UNKNOWN_ERROR", "An unexpected error occurred: " + e.getMessage());
        }
    }

    /**
     * Authenticate a user with email and password (legacy method for backward compatibility)
     *
     * This method uses the Firebase client-side SDK to authenticate the user
     * with email and password directly against Firebase Authentication.
     *
     * @return User ID if authentication is successful, null otherwise
     */
    public String authenticateUser(String email, String password) {
        AuthResult result = authenticateUserWithResult(email, password);
        return result.getUserId(); // Will be null if authentication failed
    }

    /**
     * Check if a username is already taken
     */
    private boolean isUsernameTaken(String username) throws ExecutionException, InterruptedException {
        Query query = usersCollection.whereEqualTo(USERNAME_FIELD, username);
        ApiFuture<QuerySnapshot> querySnapshot = query.get();
        return !querySnapshot.get().isEmpty();
    }

    /**
     * Get user ID by username
     * This method is kept for future use
     */
    @SuppressWarnings("unused")
    private String getUserIdByUsername(String username) throws ExecutionException, InterruptedException {
        Query query = usersCollection.whereEqualTo(USERNAME_FIELD, username);
        ApiFuture<QuerySnapshot> querySnapshot = query.get();

        if (!querySnapshot.get().isEmpty()) {
            DocumentSnapshot document = querySnapshot.get().getDocuments().get(0);
            return document.getString(USER_ID_FIELD);
        }

        return null;
    }
}
