package com.homara.app.firebase;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.io.OutputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.charset.StandardCharsets;
import java.util.HashMap;
import java.util.Map;

import org.json.JSONObject;

// Custom exception class to provide detailed authentication errors
class AuthenticationException extends Exception {
    private final String errorCode;

    public AuthenticationException(String message, String errorCode) {
        super(message);
        this.errorCode = errorCode;
    }

    public String getErrorCode() {
        return errorCode;
    }
}

/**
 * Firebase client-side authentication service that uses the Firebase REST API
 * to authenticate users with email and password.
 *
 * This class uses the Firebase Web SDK configuration to authenticate users
 * directly with Firebase Authentication.
 */
public class FirebaseClientSideAuth {

    // Firebase Web API key
    private static String API_KEY = "AIzaSyB-WtgN67M4vQN3BZXuJzkgAewPN_ZUoLw";

    /**
     * Update the API key used for Firebase authentication
     * @param newApiKey The new API key to use
     */
    public static void updateApiKey(String newApiKey) {
        if (newApiKey != null && !newApiKey.trim().isEmpty()) {
            API_KEY = newApiKey.trim();
            System.out.println("Firebase API key updated successfully");
        }
    }

    /**
     * Get the current API key
     * @return The current API key
     */
    public static String getApiKey() {
        return API_KEY;
    }

    /**
     * Get the sign-in URL with the current API key
     * @return The sign-in URL
     */
    private String getSignInUrl() {
        return "https://identitytoolkit.googleapis.com/v1/accounts:signInWithPassword?key=" + API_KEY;
    }

    /**
     * Authenticate a user with email and password using Firebase Authentication REST API
     *
     * @param email User's email
     * @param password User's password
     * @return User ID if authentication is successful
     * @throws AuthenticationException if authentication fails with specific error code
     */
    public String authenticateUser(String email, String password) throws AuthenticationException {
        try {
            System.out.println("Authenticating user with email: " + email);

            // Create the request body
            Map<String, Object> requestMap = new HashMap<>();
            requestMap.put("email", email);
            requestMap.put("password", password);
            requestMap.put("returnSecureToken", true);

            JSONObject requestBody = new JSONObject(requestMap);

            // Get the sign-in URL with the current API key
            String signInUrl = getSignInUrl();

            // Create the HTTP connection
            System.out.println("Connecting to Firebase Authentication API at: " + signInUrl);
            System.out.println("Request body: " + requestBody.toString());

            URL url = new URL(signInUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            connection.setRequestMethod("POST");
            connection.setRequestProperty("Content-Type", "application/json");
            connection.setDoOutput(true);

            // Write the request body
            try (OutputStream os = connection.getOutputStream()) {
                byte[] input = requestBody.toString().getBytes(StandardCharsets.UTF_8);
                os.write(input, 0, input.length);
                System.out.println("Request sent to Firebase Authentication API");
            }

            // Get the response
            int responseCode = connection.getResponseCode();
            System.out.println("Response code from Firebase: " + responseCode);

            if (responseCode == HttpURLConnection.HTTP_OK) {
                // Read the response
                StringBuilder response = new StringBuilder();
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getInputStream(), StandardCharsets.UTF_8))) {
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        response.append(responseLine.trim());
                    }
                }

                System.out.println("Response from Firebase: " + response.toString());

                // Parse the response
                JSONObject jsonResponse = new JSONObject(response.toString());
                String localId = jsonResponse.getString("localId");
                String idToken = jsonResponse.getString("idToken");

                System.out.println("User authenticated successfully: " + localId);

                // Store the token for future use
                storeAuthToken(idToken);

                return localId;
            } else {
                // Read the error response
                StringBuilder errorResponse = new StringBuilder();
                try (BufferedReader br = new BufferedReader(
                        new InputStreamReader(connection.getErrorStream(), StandardCharsets.UTF_8))) {
                    String responseLine;
                    while ((responseLine = br.readLine()) != null) {
                        errorResponse.append(responseLine.trim());
                    }
                }

                System.out.println("Authentication failed with detailed error: " + errorResponse.toString());

                // Try to parse the error response for more details
                try {
                    JSONObject errorJson = new JSONObject(errorResponse.toString());
                    if (errorJson.has("error")) {
                        JSONObject error = errorJson.getJSONObject("error");
                        String message = error.optString("message", "Unknown error");
                        String errorCode = message; // Default to message if no specific code

                        System.out.println("Firebase error message: " + message);

                        // Map Firebase error messages to specific error codes
                        if (message.contains("EMAIL_NOT_FOUND")) {
                            throw new AuthenticationException("Email not found. Please check your email address.", "EMAIL_NOT_FOUND");
                        } else if (message.contains("INVALID_PASSWORD")) {
                            throw new AuthenticationException("Invalid password. Please check your password.", "INVALID_PASSWORD");
                        } else if (message.contains("USER_DISABLED")) {
                            throw new AuthenticationException("This user account has been disabled.", "USER_DISABLED");
                        } else if (message.contains("TOO_MANY_ATTEMPTS_TRY_LATER")) {
                            throw new AuthenticationException("Too many failed login attempts. Please try again later.", "TOO_MANY_ATTEMPTS");
                        } else {
                            throw new AuthenticationException("Authentication failed: " + message, errorCode);
                        }
                    }
                } catch (AuthenticationException e) {
                    throw e; // Re-throw the authentication exception
                } catch (Exception e) {
                    System.out.println("Could not parse error response: " + e.getMessage());
                    throw new AuthenticationException("Authentication failed with an unknown error", "UNKNOWN_ERROR");
                }

                // If we couldn't parse a specific error, throw a generic one
                throw new AuthenticationException("Authentication failed", "AUTHENTICATION_FAILED");
            }
        } catch (AuthenticationException e) {
            // Re-throw authentication exceptions
            throw e;
        } catch (Exception e) {
            System.err.println("Error authenticating user: " + e.getMessage());
            e.printStackTrace();
            throw new AuthenticationException("Error connecting to authentication service: " + e.getMessage(), "CONNECTION_ERROR");
        }
    }

    /**
     * Store the authentication token for future use
     *
     * @param token The authentication token
     */
    private void storeAuthToken(String token) {
        // In a real application, you would store this token securely
        System.out.println("Auth token received (first 10 chars): " + token.substring(0, 10) + "...");

        // Store the token in the ApiClient for use in API calls
        try {
            // Get the ApiClient instance from Spring context if available
            com.homara.app.api.client.ApiClient apiClient = com.homara.app.spring.SpringContext.getBean(com.homara.app.api.client.ApiClient.class);
            if (apiClient != null) {
                apiClient.setAuthToken(token);
                System.out.println("Auth token stored in ApiClient for API calls");
            } else {
                System.out.println("ApiClient not available, token not stored for API calls");
            }
        } catch (Exception e) {
            System.out.println("Could not store token in ApiClient: " + e.getMessage());
        }
    }
}
