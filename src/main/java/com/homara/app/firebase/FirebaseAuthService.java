package com.homara.app.firebase;

import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.auth.FirebaseAuthException;
import com.google.firebase.auth.FirebaseToken;
import com.google.firebase.auth.UserRecord;

/**
 * Service class for Firebase Authentication operations.
 * This class provides methods for user authentication and management using Firebase Auth.
 */
public class FirebaseAuthService {
    private final FirebaseAuth auth;

    /**
     * Constructor that initializes the FirebaseAuth instance.
     */
    public FirebaseAuthService() {
        this.auth = FirebaseInitializer.getAuth();
    }

    /**
     * Verify a Firebase ID token
     *
     * @param idToken The Firebase ID token to verify
     * @return The decoded token if valid
     * @throws FirebaseAuthException if the token is invalid
     */
    public FirebaseToken verifyIdToken(String idToken) throws FirebaseAuthException {
        return auth.verifyIdToken(idToken);
    }

    /**
     * Get user by UID
     *
     * @param uid The user ID
     * @return The user record
     * @throws FirebaseAuthException if the user doesn't exist or there's an error
     */
    public UserRecord getUserByUid(String uid) throws FirebaseAuthException {
        return auth.getUser(uid);
    }

    /**
     * Get user by email
     *
     * @param email The user's email
     * @return The user record
     * @throws FirebaseAuthException if the user doesn't exist or there's an error
     */
    public UserRecord getUserByEmail(String email) throws FirebaseAuthException {
        return auth.getUserByEmail(email);
    }

    /**
     * Create a new user
     *
     * @param email The user's email
     * @param password The user's password
     * @param displayName The user's display name
     * @return The created user record
     * @throws FirebaseAuthException if there's an error creating the user
     */
    public UserRecord createUser(String email, String password, String displayName) throws FirebaseAuthException {
        UserRecord.CreateRequest request = new UserRecord.CreateRequest()
            .setEmail(email)
            .setPassword(password)
            .setDisplayName(displayName)
            .setEmailVerified(false);

        return auth.createUser(request);
    }

    /**
     * Update a user
     *
     * @param uid The user ID
     * @param displayName The new display name
     * @return The updated user record
     * @throws FirebaseAuthException if there's an error updating the user
     */
    public UserRecord updateUser(String uid, String displayName) throws FirebaseAuthException {
        UserRecord.UpdateRequest request = new UserRecord.UpdateRequest(uid)
            .setDisplayName(displayName);

        return auth.updateUser(request);
    }

    /**
     * Delete a user
     *
     * @param uid The user ID
     * @throws FirebaseAuthException if there's an error deleting the user
     */
    public void deleteUser(String uid) throws FirebaseAuthException {
        auth.deleteUser(uid);
    }

    /**
     * Authenticate a user with email and password using the existing client-side auth
     * This method is a bridge to the existing FirebaseClientSideAuth implementation
     *
     * @param email The user's email
     * @param password The user's password
     * @return Authentication result containing user ID or error information
     */
    public AuthResult authenticateUser(String email, String password) {
        FirebaseClientSideAuth clientAuth = new FirebaseClientSideAuth();
        try {
            System.out.println("Authenticating user with email: " + email);
            System.out.println("Using Firebase client-side authentication");

            // Use the client-side auth service to authenticate the user
            String userId = clientAuth.authenticateUser(email, password);

            System.out.println("User authenticated successfully: " + userId);
            return AuthResult.success(userId);
        } catch (Exception e) {
            System.err.println("Authentication failed: " + e.getMessage());

            String errorCode = "UNKNOWN_ERROR";
            if (e instanceof AuthenticationException) {
                errorCode = ((AuthenticationException) e).getErrorCode();
            }
            return AuthResult.error(errorCode, e.getMessage());
        }
    }

    /**
     * Result class to hold authentication result with error information
     */
    public static class AuthResult {
        private final String userId;
        private final String errorCode;
        private final String errorMessage;

        private AuthResult(String userId, String errorCode, String errorMessage) {
            this.userId = userId;
            this.errorCode = errorCode;
            this.errorMessage = errorMessage;
        }

        public static AuthResult success(String userId) {
            return new AuthResult(userId, null, null);
        }

        public static AuthResult error(String errorCode, String errorMessage) {
            return new AuthResult(null, errorCode, errorMessage);
        }

        public boolean isSuccess() {
            return userId != null;
        }

        public String getUserId() {
            return userId;
        }

        public String getErrorCode() {
            return errorCode;
        }

        public String getErrorMessage() {
            return errorMessage;
        }
    }
}
