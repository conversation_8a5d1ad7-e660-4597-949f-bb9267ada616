package com.homara.app.firebase;

/**
 * Simplified Firebase initializer for client-side authentication only
 * Since we're using REST API for authentication, we don't need the Firebase Admin SDK
 */
public class FirebaseInitializer {

    /**
     * Initialize Firebase (simplified version)
     * @return true if initialization was successful, false otherwise
     */
    public static boolean initialize() {
        try {
            System.out.println("Initializing Firebase client-side authentication...");

            // For client-side authentication using REST API, we just need to verify
            // that we have the API key configured
            String apiKey = FirebaseClientSideAuth.getApiKey();
            if (apiKey == null || apiKey.isEmpty()) {
                System.err.println("Firebase API key is not configured");
                return false;
            }

            System.out.println("Firebase client-side authentication initialized successfully.");
            return true;

        } catch (Exception e) {
            System.err.println("Error initializing Firebase: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }
}
