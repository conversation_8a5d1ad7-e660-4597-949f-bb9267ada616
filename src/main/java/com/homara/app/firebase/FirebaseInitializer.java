package com.homara.app.firebase;

import com.google.auth.oauth2.GoogleCredentials;
import com.google.cloud.firestore.Firestore;
import com.google.firebase.FirebaseApp;
import com.google.firebase.FirebaseOptions;
import com.google.firebase.auth.FirebaseAuth;
import com.google.firebase.cloud.FirestoreClient;

import java.io.FileInputStream;
import java.io.IOException;
import java.io.InputStream;

public class FirebaseInitializer {
    private static Firestore firestoreInstance;
    private static FirebaseApp firebaseApp;

    /**
     * Initialize Firebase with the configuration file
     * @return true if initialization was successful, false otherwise
     */
    public static boolean initialize() {
        try {
            System.out.println("Initializing Firebase...");

            // Check if Firebase is already initialized
            if (!FirebaseApp.getApps().isEmpty()) {
                System.out.println("Firebase is already initialized.");
                firebaseApp = FirebaseApp.getInstance();

                // Make sure we have a Firestore instance
                if (firestoreInstance == null) {
                    firestoreInstance = FirestoreClient.getFirestore();
                }

                return true;
            }

            // Load the Firebase configuration file
            InputStream serviceAccount = FirebaseInitializer.class.getResourceAsStream("/firebase-config.json");

            if (serviceAccount == null) {
                // If not found in resources, try to load from file system (for development)
                try {
                    System.out.println("Firebase config not found in resources, trying file system...");
                    serviceAccount = new FileInputStream("src/main/resources/firebase-config.json");
                } catch (IOException e) {
                    System.err.println("Firebase configuration file not found: " + e.getMessage());
                    return false;
                }
            }

            System.out.println("Firebase configuration file loaded successfully.");

            // Configure Firebase
            FirebaseOptions options = FirebaseOptions.builder()
                    .setCredentials(GoogleCredentials.fromStream(serviceAccount))
                    .setDatabaseUrl("https://homara-77657.firebaseio.com")
                    .build();

            // Initialize Firebase
            firebaseApp = FirebaseApp.initializeApp(options);
            System.out.println("Firebase has been initialized successfully.");

            // Get Firestore instance
            firestoreInstance = FirestoreClient.getFirestore();
            System.out.println("Firestore instance created successfully.");

            return true;
        } catch (IOException e) {
            System.err.println("Error initializing Firebase: " + e.getMessage());
            e.printStackTrace();
            return false;
        }
    }

    public static Firestore getFirestore() {
        if (firestoreInstance == null) {
            initialize();
        }
        return firestoreInstance;
    }

    public static FirebaseAuth getAuth() {
        if (firebaseApp == null) {
            initialize();
        }
        return FirebaseAuth.getInstance();
    }
}
