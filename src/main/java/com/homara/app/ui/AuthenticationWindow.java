package com.homara.app.ui;

import com.homara.app.firebase.FirebaseClientSideAuth;
import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.GridPane;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Authentication window for user login using Firebase Authentication
 */
public class AuthenticationWindow {
    
    private Stage stage;
    private TextField emailField;
    private PasswordField passwordField;
    private Button loginButton;
    private Label statusLabel;
    private FirebaseClientSideAuth authService;
    
    public AuthenticationWindow() {
        this.authService = new FirebaseClientSideAuth();
        initializeUI();
    }
    
    private void initializeUI() {
        stage = new Stage();
        stage.setTitle("Homara App - Login");
        stage.setResizable(false);
        
        // Create main layout
        VBox mainLayout = new VBox(20);
        mainLayout.setPadding(new Insets(30));
        mainLayout.setAlignment(Pos.CENTER);
        
        // Title
        Label titleLabel = new Label("Welcome to Homara App");
        titleLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold;");
        
        // Login form
        GridPane loginForm = createLoginForm();
        
        // Status label
        statusLabel = new Label();
        statusLabel.setStyle("-fx-text-fill: red;");
        
        // Add components to main layout
        mainLayout.getChildren().addAll(titleLabel, loginForm, statusLabel);
        
        // Create scene
        Scene scene = new Scene(mainLayout, 400, 300);
        stage.setScene(scene);
        
        // Handle window close
        stage.setOnCloseRequest(e -> Platform.exit());
    }
    
    private GridPane createLoginForm() {
        GridPane grid = new GridPane();
        grid.setAlignment(Pos.CENTER);
        grid.setHgap(10);
        grid.setVgap(10);
        
        // Email field
        Label emailLabel = new Label("Email:");
        emailField = new TextField();
        emailField.setPromptText("Enter your email");
        emailField.setPrefWidth(250);
        
        // Password field
        Label passwordLabel = new Label("Password:");
        passwordField = new PasswordField();
        passwordField.setPromptText("Enter your password");
        passwordField.setPrefWidth(250);
        
        // Login button
        loginButton = new Button("Login");
        loginButton.setPrefWidth(100);
        loginButton.setDefaultButton(true);
        loginButton.setOnAction(e -> handleLogin());
        
        // Handle Enter key in password field
        passwordField.setOnAction(e -> handleLogin());
        
        // Add components to grid
        grid.add(emailLabel, 0, 0);
        grid.add(emailField, 1, 0);
        grid.add(passwordLabel, 0, 1);
        grid.add(passwordField, 1, 1);
        
        // Button container
        HBox buttonBox = new HBox();
        buttonBox.setAlignment(Pos.CENTER);
        buttonBox.getChildren().add(loginButton);
        grid.add(buttonBox, 0, 2, 2, 1);
        
        return grid;
    }
    
    private void handleLogin() {
        String email = emailField.getText().trim();
        String password = passwordField.getText();
        
        if (email.isEmpty() || password.isEmpty()) {
            showStatus("Please enter both email and password", true);
            return;
        }
        
        // Disable login button during authentication
        loginButton.setDisabled(true);
        showStatus("Authenticating...", false);
        
        // Perform authentication in background thread
        Thread authThread = new Thread(() -> {
            try {
                String userId = authService.authenticateUser(email, password);
                
                // Extract username from email (everything before @)
                String username = extractUsernameFromEmail(email);
                
                Platform.runLater(() -> {
                    showStatus("Login successful! Welcome " + username, false);
                    
                    // Open main window after successful login
                    openMainWindow(username, userId);
                    
                    // Close login window
                    stage.close();
                });
                
            } catch (Exception e) {
                Platform.runLater(() -> {
                    showStatus("Login failed: " + e.getMessage(), true);
                    loginButton.setDisabled(false);
                });
            }
        });
        
        authThread.setDaemon(true);
        authThread.start();
    }
    
    private String extractUsernameFromEmail(String email) {
        if (email == null || email.isEmpty()) {
            return "User";
        }
        
        int atIndex = email.indexOf('@');
        if (atIndex > 0) {
            return email.substring(0, atIndex);
        }
        
        return email; // Return full email if no @ found
    }
    
    private void showStatus(String message, boolean isError) {
        statusLabel.setText(message);
        if (isError) {
            statusLabel.setStyle("-fx-text-fill: red;");
        } else {
            statusLabel.setStyle("-fx-text-fill: green;");
        }
    }
    
    private void openMainWindow(String username, String userId) {
        MainWindow mainWindow = new MainWindow(username, userId);
        mainWindow.show();
    }
    
    public void show() {
        stage.show();
    }
}
