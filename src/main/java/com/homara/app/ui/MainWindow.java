package com.homara.app.ui;

import javafx.application.Platform;
import javafx.geometry.Insets;
import javafx.geometry.Pos;
import javafx.scene.Scene;
import javafx.scene.control.*;
import javafx.scene.layout.BorderPane;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Main application window with tabs for different sections
 */
public class MainWindow {
    
    private Stage stage;
    private String username;
    private String userId;
    private TabPane tabPane;
    
    public MainWindow(String username, String userId) {
        this.username = username;
        this.userId = userId;
        initializeUI();
    }
    
    private void initializeUI() {
        stage = new Stage();
        stage.setTitle("Homara App - Welcome " + username);
        stage.setMaximized(true);
        
        // Create main layout
        BorderPane mainLayout = new BorderPane();
        
        // Create header
        VBox header = createHeader();
        mainLayout.setTop(header);
        
        // Create tab pane
        tabPane = createTabPane();
        mainLayout.setCenter(tabPane);
        
        // Create scene
        Scene scene = new Scene(mainLayout, 1200, 800);
        stage.setScene(scene);
        
        // Handle window close
        stage.setOnCloseRequest(e -> Platform.exit());
    }
    
    private VBox createHeader() {
        VBox header = new VBox(10);
        header.setPadding(new Insets(20));
        header.setStyle("-fx-background-color: #2c3e50;");
        
        // Welcome message
        Label welcomeLabel = new Label("Welcome, " + username + "!");
        welcomeLabel.setStyle("-fx-font-size: 24px; -fx-font-weight: bold; -fx-text-fill: white;");
        
        // User info
        Label userInfoLabel = new Label("User ID: " + userId);
        userInfoLabel.setStyle("-fx-font-size: 12px; -fx-text-fill: #bdc3c7;");
        
        // Logout button
        Button logoutButton = new Button("Logout");
        logoutButton.setStyle("-fx-background-color: #e74c3c; -fx-text-fill: white;");
        logoutButton.setOnAction(e -> handleLogout());
        
        header.getChildren().addAll(welcomeLabel, userInfoLabel, logoutButton);
        header.setAlignment(Pos.CENTER_LEFT);
        
        return header;
    }
    
    private TabPane createTabPane() {
        TabPane tabPane = new TabPane();
        
        // Home tab
        Tab homeTab = new Tab("Home");
        homeTab.setClosable(false);
        homeTab.setContent(createHomeContent());
        
        // Payments tab
        Tab paymentsTab = new Tab("Payments");
        paymentsTab.setClosable(false);
        paymentsTab.setContent(createPaymentsContent());
        
        // Profile tab
        Tab profileTab = new Tab("Profile");
        profileTab.setClosable(false);
        profileTab.setContent(createProfileContent());
        
        // Settings tab
        Tab settingsTab = new Tab("Settings");
        settingsTab.setClosable(false);
        settingsTab.setContent(createSettingsContent());
        
        tabPane.getTabs().addAll(homeTab, paymentsTab, profileTab, settingsTab);
        
        return tabPane;
    }
    
    private VBox createHomeContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(30));
        content.setAlignment(Pos.TOP_CENTER);
        
        Label titleLabel = new Label("Home Dashboard");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold;");
        
        Label descriptionLabel = new Label("Welcome to your Homara App dashboard!");
        descriptionLabel.setStyle("-fx-font-size: 16px;");
        
        // Quick stats or info cards could go here
        VBox statsBox = new VBox(10);
        statsBox.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 20; -fx-background-radius: 10;");
        
        Label statsTitle = new Label("Quick Stats");
        statsTitle.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        Label userStat = new Label("• Logged in as: " + username);
        Label statusStat = new Label("• Account Status: Active");
        Label lastLoginStat = new Label("• Last Login: Today");
        
        statsBox.getChildren().addAll(statsTitle, userStat, statusStat, lastLoginStat);
        
        content.getChildren().addAll(titleLabel, descriptionLabel, statsBox);
        
        return content;
    }
    
    private VBox createPaymentsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(30));
        content.setAlignment(Pos.TOP_CENTER);
        
        Label titleLabel = new Label("Payments");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold;");
        
        Label descriptionLabel = new Label("Manage your payments and billing information");
        descriptionLabel.setStyle("-fx-font-size: 16px;");
        
        // Payment options
        VBox paymentBox = new VBox(15);
        paymentBox.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 20; -fx-background-radius: 10;");
        
        Label paymentTitle = new Label("Payment Options");
        paymentTitle.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        Button addPaymentButton = new Button("Add Payment Method");
        addPaymentButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-padding: 10;");
        
        Button viewHistoryButton = new Button("View Payment History");
        viewHistoryButton.setStyle("-fx-background-color: #2ecc71; -fx-text-fill: white; -fx-padding: 10;");
        
        Button manageBillingButton = new Button("Manage Billing");
        manageBillingButton.setStyle("-fx-background-color: #f39c12; -fx-text-fill: white; -fx-padding: 10;");
        
        paymentBox.getChildren().addAll(paymentTitle, addPaymentButton, viewHistoryButton, manageBillingButton);
        
        content.getChildren().addAll(titleLabel, descriptionLabel, paymentBox);
        
        return content;
    }
    
    private VBox createProfileContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(30));
        content.setAlignment(Pos.TOP_CENTER);
        
        Label titleLabel = new Label("Profile");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold;");
        
        // Profile information
        VBox profileBox = new VBox(15);
        profileBox.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 20; -fx-background-radius: 10;");
        
        Label profileTitle = new Label("Profile Information");
        profileTitle.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        Label usernameLabel = new Label("Username: " + username);
        Label userIdLabel = new Label("User ID: " + userId);
        Label accountTypeLabel = new Label("Account Type: Standard");
        
        Button editProfileButton = new Button("Edit Profile");
        editProfileButton.setStyle("-fx-background-color: #3498db; -fx-text-fill: white; -fx-padding: 10;");
        
        profileBox.getChildren().addAll(profileTitle, usernameLabel, userIdLabel, accountTypeLabel, editProfileButton);
        
        content.getChildren().addAll(titleLabel, profileBox);
        
        return content;
    }
    
    private VBox createSettingsContent() {
        VBox content = new VBox(20);
        content.setPadding(new Insets(30));
        content.setAlignment(Pos.TOP_CENTER);
        
        Label titleLabel = new Label("Settings");
        titleLabel.setStyle("-fx-font-size: 28px; -fx-font-weight: bold;");
        
        // Settings options
        VBox settingsBox = new VBox(15);
        settingsBox.setStyle("-fx-background-color: #ecf0f1; -fx-padding: 20; -fx-background-radius: 10;");
        
        Label settingsTitle = new Label("Application Settings");
        settingsTitle.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");
        
        CheckBox notificationsCheck = new CheckBox("Enable Notifications");
        notificationsCheck.setSelected(true);
        
        CheckBox autoLoginCheck = new CheckBox("Remember Login");
        autoLoginCheck.setSelected(false);
        
        Button saveSettingsButton = new Button("Save Settings");
        saveSettingsButton.setStyle("-fx-background-color: #2ecc71; -fx-text-fill: white; -fx-padding: 10;");
        
        settingsBox.getChildren().addAll(settingsTitle, notificationsCheck, autoLoginCheck, saveSettingsButton);
        
        content.getChildren().addAll(titleLabel, settingsBox);
        
        return content;
    }
    
    private void handleLogout() {
        Alert alert = new Alert(Alert.AlertType.CONFIRMATION);
        alert.setTitle("Logout");
        alert.setHeaderText("Are you sure you want to logout?");
        alert.setContentText("You will need to login again to access the application.");
        
        alert.showAndWait().ifPresent(response -> {
            if (response == ButtonType.OK) {
                stage.close();
                
                // Show login window again
                AuthenticationWindow authWindow = new AuthenticationWindow();
                authWindow.show();
            }
        });
    }
    
    public void show() {
        stage.show();
    }
}
