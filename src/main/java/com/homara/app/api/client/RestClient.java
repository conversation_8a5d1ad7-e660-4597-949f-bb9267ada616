package com.homara.app.api.client;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.homara.app.api.model.ApiResponse;

import java.io.IOException;
import java.net.URI;
import java.net.http.HttpClient;
import java.net.http.HttpRequest;
import java.net.http.HttpResponse;
import java.time.Duration;
import java.util.Map;

/**
 * Base REST client for making HTTP requests to backend services.
 */
public class RestClient {

    private final HttpClient httpClient;
    private final ObjectMapper objectMapper;
    private final String baseUrl;

    /**
     * Creates a new RestClient with the specified base URL.
     *
     * @param baseUrl the base URL for all requests
     */
    public RestClient(String baseUrl) {
        this.baseUrl = baseUrl;
        this.httpClient = HttpClient.newBuilder()
                .version(HttpClient.Version.HTTP_2)
                .connectTimeout(Duration.ofSeconds(10))
                .build();
        this.objectMapper = new ObjectMapper();
    }

    /**
     * Performs a GET request to the specified endpoint.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> get(String endpoint, Class<T> responseType) throws IOException, InterruptedException {
        return get(endpoint, responseType, null, null);
    }

    /**
     * Performs a GET request to the specified endpoint with additional headers.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param headerName the name of the additional header
     * @param headerValue the value of the additional header
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> get(String endpoint, Class<T> responseType, String headerName, String headerValue) throws IOException, InterruptedException {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .GET()
                .uri(URI.create(baseUrl + endpoint))
                .header("Content-Type", "application/json");

        if (headerName != null && headerValue != null) {
            requestBuilder.header(headerName, headerValue);
        }

        HttpRequest request = requestBuilder.build();
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        return parseResponse(response, responseType);
    }

    /**
     * Performs a POST request to the specified endpoint with the given body.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> post(String endpoint, Object body, Class<T> responseType) throws IOException, InterruptedException {
        return post(endpoint, body, responseType, null, null);
    }

    /**
     * Performs a POST request to the specified endpoint with the given body and additional headers.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param headerName the name of the additional header
     * @param headerValue the value of the additional header
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> post(String endpoint, Object body, Class<T> responseType, String headerName, String headerValue) throws IOException, InterruptedException {
        String jsonBody = objectMapper.writeValueAsString(body);

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .POST(HttpRequest.BodyPublishers.ofString(jsonBody))
                .uri(URI.create(baseUrl + endpoint))
                .header("Content-Type", "application/json");

        if (headerName != null && headerValue != null) {
            requestBuilder.header(headerName, headerValue);
        }

        HttpRequest request = requestBuilder.build();
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        return parseResponse(response, responseType);
    }

    /**
     * Performs a PUT request to the specified endpoint with the given body.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> put(String endpoint, Object body, Class<T> responseType) throws IOException, InterruptedException {
        return put(endpoint, body, responseType, null, null);
    }

    /**
     * Performs a PUT request to the specified endpoint with the given body and additional headers.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param headerName the name of the additional header
     * @param headerValue the value of the additional header
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> put(String endpoint, Object body, Class<T> responseType, String headerName, String headerValue) throws IOException, InterruptedException {
        String jsonBody = objectMapper.writeValueAsString(body);

        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .PUT(HttpRequest.BodyPublishers.ofString(jsonBody))
                .uri(URI.create(baseUrl + endpoint))
                .header("Content-Type", "application/json");

        if (headerName != null && headerValue != null) {
            requestBuilder.header(headerName, headerValue);
        }

        HttpRequest request = requestBuilder.build();
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        return parseResponse(response, responseType);
    }

    /**
     * Performs a DELETE request to the specified endpoint.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> delete(String endpoint, Class<T> responseType) throws IOException, InterruptedException {
        return delete(endpoint, responseType, null, null);
    }

    /**
     * Performs a DELETE request to the specified endpoint with additional headers.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param headerName the name of the additional header
     * @param headerValue the value of the additional header
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> delete(String endpoint, Class<T> responseType, String headerName, String headerValue) throws IOException, InterruptedException {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .DELETE()
                .uri(URI.create(baseUrl + endpoint))
                .header("Content-Type", "application/json");

        if (headerName != null && headerValue != null) {
            requestBuilder.header(headerName, headerValue);
        }

        HttpRequest request = requestBuilder.build();
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        return parseResponse(response, responseType);
    }

    /**
     * Performs a GET request to the specified endpoint with a TypeReference.
     *
     * @param endpoint the API endpoint
     * @param typeReference the TypeReference for the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> get(String endpoint, TypeReference<T> typeReference) throws IOException, InterruptedException {
        return get(endpoint, typeReference, null, null);
    }

    /**
     * Performs a GET request to the specified endpoint with a TypeReference and additional headers.
     *
     * @param endpoint the API endpoint
     * @param typeReference the TypeReference for the expected response
     * @param headerName the name of the additional header
     * @param headerValue the value of the additional header
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> get(String endpoint, TypeReference<T> typeReference, String headerName, String headerValue) throws IOException, InterruptedException {
        HttpRequest.Builder requestBuilder = HttpRequest.newBuilder()
                .GET()
                .uri(URI.create(baseUrl + endpoint))
                .header("Content-Type", "application/json");

        if (headerName != null && headerValue != null) {
            requestBuilder.header(headerName, headerValue);
        }

        HttpRequest request = requestBuilder.build();
        HttpResponse<String> response = httpClient.send(request, HttpResponse.BodyHandlers.ofString());
        return parseResponse(response, typeReference);
    }

    /**
     * Parses the HTTP response into an ApiResponse object.
     *
     * @param response the HTTP response
     * @param responseType the class of the expected response data
     * @param <T> the type of the response data
     * @return the parsed API response
     * @throws IOException if an I/O error occurs during parsing
     */
    private <T> ApiResponse<T> parseResponse(HttpResponse<String> response, Class<T> responseType) throws IOException {
        int statusCode = response.statusCode();
        String body = response.body();

        if (statusCode >= 200 && statusCode < 300) {
            T data = objectMapper.readValue(body, responseType);
            return new ApiResponse<>(true, null, data);
        } else {
            String errorMessage = "Request failed with status code: " + statusCode;
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> errorMap = objectMapper.readValue(body, Map.class);
                if (errorMap.containsKey("message")) {
                    errorMessage = (String) errorMap.get("message");
                }
            } catch (JsonProcessingException e) {
                // If we can't parse the error message, just use the default one
            }
            return new ApiResponse<>(false, errorMessage, null);
        }
    }

    /**
     * Parses the HTTP response into an ApiResponse object using a TypeReference.
     *
     * @param response the HTTP response
     * @param typeReference the TypeReference for the expected response data
     * @param <T> the type of the response data
     * @return the parsed API response
     * @throws IOException if an I/O error occurs during parsing
     */
    private <T> ApiResponse<T> parseResponse(HttpResponse<String> response, TypeReference<T> typeReference) throws IOException {
        int statusCode = response.statusCode();
        String body = response.body();

        if (statusCode >= 200 && statusCode < 300) {
            T data = objectMapper.readValue(body, typeReference);
            return new ApiResponse<>(true, null, data);
        } else {
            String errorMessage = "Request failed with status code: " + statusCode;
            try {
                @SuppressWarnings("unchecked")
                Map<String, Object> errorMap = objectMapper.readValue(body, Map.class);
                if (errorMap.containsKey("message")) {
                    errorMessage = (String) errorMap.get("message");
                }
            } catch (JsonProcessingException e) {
                // If we can't parse the error message, just use the default one
            }
            return new ApiResponse<>(false, errorMessage, null);
        }
    }
}
