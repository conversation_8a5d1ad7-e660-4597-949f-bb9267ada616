package com.homara.app.api.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.community.CommunityDto;

import java.io.IOException;
import java.util.List;

/**
 * API client for community-related operations.
 */
public class CommunityApiClient extends ApiClient {

    private static final String COMMUNITIES_ENDPOINT = "/api/communities";

    /**
     * Creates a new CommunityApiClient with the default API URL.
     */
    public CommunityApiClient() {
        super();
    }

    /**
     * Creates a new CommunityApiClient with the specified API URL.
     *
     * @param baseUrl the base URL for the API
     */
    public CommunityApiClient(String baseUrl) {
        super(baseUrl);
    }

    /**
     * Gets all communities.
     *
     * @return a list of all communities
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<CommunityDto>> getAllCommunities() throws IOException, InterruptedException {
        TypeReference<List<CommunityDto>> typeRef = new TypeReference<List<CommunityDto>>() {};
        return authenticatedGet(COMMUNITIES_ENDPOINT, typeRef);
    }

    /**
     * Gets a community by ID.
     *
     * @param communityId the ID of the community to get
     * @return the community with the specified ID
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<CommunityDto> getCommunityById(String communityId) throws IOException, InterruptedException {
        return authenticatedGet(COMMUNITIES_ENDPOINT + "/" + communityId, CommunityDto.class);
    }

    /**
     * Creates a new community.
     *
     * @param community the community to create
     * @return the created community
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<CommunityDto> createCommunity(CommunityDto community) throws IOException, InterruptedException {
        return authenticatedPost(COMMUNITIES_ENDPOINT, community, CommunityDto.class);
    }

    /**
     * Updates an existing community.
     *
     * @param communityId the ID of the community to update
     * @param community the updated community data
     * @return the updated community
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<CommunityDto> updateCommunity(String communityId, CommunityDto community) throws IOException, InterruptedException {
        return authenticatedPut(COMMUNITIES_ENDPOINT + "/" + communityId, community, CommunityDto.class);
    }

    /**
     * Deletes a community.
     *
     * @param communityId the ID of the community to delete
     * @return the response indicating success or failure
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<Void> deleteCommunity(String communityId) throws IOException, InterruptedException {
        return authenticatedDelete(COMMUNITIES_ENDPOINT + "/" + communityId, Void.class);
    }

    /**
     * Gets all communities for a user.
     *
     * @param userId the ID of the user
     * @return a list of communities for the user
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<CommunityDto>> getUserCommunities(String userId) throws IOException, InterruptedException {
        TypeReference<List<CommunityDto>> typeRef = new TypeReference<List<CommunityDto>>() {};
        return authenticatedGet(COMMUNITIES_ENDPOINT + "/user/" + userId, typeRef);
    }
}
