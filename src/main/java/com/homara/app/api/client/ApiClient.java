package com.homara.app.api.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.homara.app.api.model.ApiResponse;

import java.io.IOException;

/**
 * API-specific client extending the base RestClient.
 * This client adds application-specific functionality on top of the base REST client.
 */
public class ApiClient extends RestClient {

    private static final String DEFAULT_API_URL = "https://api.homara.community";
    private String authToken;

    /**
     * Creates a new ApiClient with the default API URL.
     */
    public ApiClient() {
        super(DEFAULT_API_URL);
    }

    /**
     * Creates a new ApiClient with the specified API URL.
     *
     * @param baseUrl the base URL for the API
     */
    public ApiClient(String baseUrl) {
        super(baseUrl);
    }

    /**
     * Sets the authentication token for subsequent requests.
     *
     * @param authToken the authentication token
     */
    public void setAuthToken(String authToken) {
        this.authToken = authToken;
    }

    /**
     * Gets the current authentication token.
     *
     * @return the current authentication token
     */
    public String getAuthToken() {
        return authToken;
    }

    /**
     * Performs an authenticated GET request to the specified endpoint.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> authenticatedGet(String endpoint, Class<T> responseType) throws IOException, InterruptedException {
        // Add authentication header if token is available
        if (authToken != null && !authToken.isEmpty()) {
            return get(endpoint, responseType, "Authorization", "Bearer " + authToken);
        }
        return get(endpoint, responseType);
    }

    /**
     * Performs an authenticated GET request to the specified endpoint with a TypeReference.
     *
     * @param endpoint the API endpoint
     * @param typeReference the TypeReference for the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> authenticatedGet(String endpoint, TypeReference<T> typeReference) throws IOException, InterruptedException {
        // Add authentication header if token is available
        if (authToken != null && !authToken.isEmpty()) {
            return get(endpoint, typeReference, "Authorization", "Bearer " + authToken);
        }
        return get(endpoint, typeReference);
    }

    /**
     * Performs an authenticated POST request to the specified endpoint with the given body.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> authenticatedPost(String endpoint, Object body, Class<T> responseType) throws IOException, InterruptedException {
        // Add authentication header if token is available
        if (authToken != null && !authToken.isEmpty()) {
            return post(endpoint, body, responseType, "Authorization", "Bearer " + authToken);
        }
        return post(endpoint, body, responseType);
    }

    /**
     * Performs an authenticated PUT request to the specified endpoint with the given body.
     *
     * @param endpoint the API endpoint
     * @param body the request body
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> authenticatedPut(String endpoint, Object body, Class<T> responseType) throws IOException, InterruptedException {
        // Add authentication header if token is available
        if (authToken != null && !authToken.isEmpty()) {
            return put(endpoint, body, responseType, "Authorization", "Bearer " + authToken);
        }
        return put(endpoint, body, responseType);
    }

    /**
     * Performs an authenticated DELETE request to the specified endpoint.
     *
     * @param endpoint the API endpoint
     * @param responseType the class of the expected response
     * @param <T> the type of the response
     * @return the response object
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public <T> ApiResponse<T> authenticatedDelete(String endpoint, Class<T> responseType) throws IOException, InterruptedException {
        // Add authentication header if token is available
        if (authToken != null && !authToken.isEmpty()) {
            return delete(endpoint, responseType, "Authorization", "Bearer " + authToken);
        }
        return delete(endpoint, responseType);
    }
}
