package com.homara.app.api.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.user.UserDto;

import java.io.IOException;
import java.util.List;

/**
 * API client for user-related operations.
 */
public class UserApiClient extends ApiClient {

    private static final String USERS_ENDPOINT = "/api/users";
    private static final String AUTH_ENDPOINT = "/api/auth";

    /**
     * Creates a new UserApiClient with the default API URL.
     */
    public UserApiClient() {
        super();
    }

    /**
     * Creates a new UserApiClient with the specified API URL.
     *
     * @param baseUrl the base URL for the API
     */
    public UserApiClient(String baseUrl) {
        super(baseUrl);
    }

    /**
     * Gets all users.
     *
     * @return a list of all users
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<UserDto>> getAllUsers() throws IOException, InterruptedException {
        TypeReference<List<UserDto>> typeRef = new TypeReference<List<UserDto>>() {};
        return authenticatedGet(USERS_ENDPOINT, typeRef);
    }

    /**
     * Gets a user by ID.
     *
     * @param userId the ID of the user to get
     * @return the user with the specified ID
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<UserDto> getUserById(String userId) throws IOException, InterruptedException {
        return authenticatedGet(USERS_ENDPOINT + "/" + userId, UserDto.class);
    }

    /**
     * Gets a user by email.
     *
     * @param email the email of the user to get
     * @return the user with the specified email
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<UserDto> getUserByEmail(String email) throws IOException, InterruptedException {
        return authenticatedGet(USERS_ENDPOINT + "/email/" + email, UserDto.class);
    }

    /**
     * Creates a new user.
     *
     * @param user the user to create
     * @return the created user
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<UserDto> createUser(UserDto user) throws IOException, InterruptedException {
        return authenticatedPost(USERS_ENDPOINT, user, UserDto.class);
    }

    /**
     * Updates an existing user.
     *
     * @param userId the ID of the user to update
     * @param user the updated user data
     * @return the updated user
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<UserDto> updateUser(String userId, UserDto user) throws IOException, InterruptedException {
        return authenticatedPut(USERS_ENDPOINT + "/" + userId, user, UserDto.class);
    }

    /**
     * Deletes a user.
     *
     * @param userId the ID of the user to delete
     * @return the response indicating success or failure
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<Void> deleteUser(String userId) throws IOException, InterruptedException {
        return authenticatedDelete(USERS_ENDPOINT + "/" + userId, Void.class);
    }

    /**
     * Gets all users in a community.
     *
     * @param communityId the ID of the community
     * @return a list of users in the community
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<UserDto>> getCommunityUsers(String communityId) throws IOException, InterruptedException {
        TypeReference<List<UserDto>> typeRef = new TypeReference<List<UserDto>>() {};
        return authenticatedGet(USERS_ENDPOINT + "/community/" + communityId, typeRef);
    }

    /**
     * Gets the desktop profile for the authenticated user.
     * This endpoint is specifically for the desktop application.
     *
     * @return the user profile for the desktop app
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<UserDto> getDesktopProfile() throws IOException, InterruptedException {
        return authenticatedGet(AUTH_ENDPOINT + "/desktop-profile", UserDto.class);
    }
}
