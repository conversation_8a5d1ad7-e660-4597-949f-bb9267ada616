package com.homara.app.api.client;

import com.fasterxml.jackson.core.type.TypeReference;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.point.PointDto;

import java.io.IOException;
import java.util.List;

/**
 * API client for point-related operations.
 */
public class PointApiClient extends ApiClient {

    private static final String POINTS_ENDPOINT = "/api/points";

    /**
     * Creates a new PointApiClient with the default API URL.
     */
    public PointApiClient() {
        super();
    }

    /**
     * Creates a new PointApiClient with the specified API URL.
     *
     * @param baseUrl the base URL for the API
     */
    public PointApiClient(String baseUrl) {
        super(baseUrl);
    }

    /**
     * Gets all points.
     *
     * @return a list of all points
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<PointDto>> getAllPoints() throws IOException, InterruptedException {
        // Use a TypeReference to specify the generic type
        TypeReference<List<PointDto>> typeRef = new TypeReference<List<PointDto>>() {};
        return authenticatedGet(POINTS_ENDPOINT, typeRef);
    }

    /**
     * Gets a point by ID.
     *
     * @param pointId the ID of the point to get
     * @return the point with the specified ID
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<PointDto> getPointById(String pointId) throws IOException, InterruptedException {
        return authenticatedGet(POINTS_ENDPOINT + "/" + pointId, PointDto.class);
    }

    /**
     * Creates a new point.
     *
     * @param point the point to create
     * @return the created point
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<PointDto> createPoint(PointDto point) throws IOException, InterruptedException {
        return authenticatedPost(POINTS_ENDPOINT, point, PointDto.class);
    }

    /**
     * Updates an existing point.
     *
     * @param pointId the ID of the point to update
     * @param point the updated point data
     * @return the updated point
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<PointDto> updatePoint(String pointId, PointDto point) throws IOException, InterruptedException {
        return authenticatedPut(POINTS_ENDPOINT + "/" + pointId, point, PointDto.class);
    }

    /**
     * Deletes a point.
     *
     * @param pointId the ID of the point to delete
     * @return the response indicating success or failure
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<Void> deletePoint(String pointId) throws IOException, InterruptedException {
        return authenticatedDelete(POINTS_ENDPOINT + "/" + pointId, Void.class);
    }

    /**
     * Gets all points for a community.
     *
     * @param communityId the ID of the community
     * @return a list of points for the community
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<PointDto>> getCommunityPoints(String communityId) throws IOException, InterruptedException {
        TypeReference<List<PointDto>> typeRef = new TypeReference<List<PointDto>>() {};
        return authenticatedGet(POINTS_ENDPOINT + "/community/" + communityId, typeRef);
    }

    /**
     * Gets all points for a user.
     *
     * @param userId the ID of the user
     * @return a list of points for the user
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<List<PointDto>> getUserPoints(String userId) throws IOException, InterruptedException {
        TypeReference<List<PointDto>> typeRef = new TypeReference<List<PointDto>>() {};
        return authenticatedGet(POINTS_ENDPOINT + "/user/" + userId, typeRef);
    }

    /**
     * Updates the position of a point.
     *
     * @param pointId the ID of the point to update
     * @param position the new position
     * @return the updated point
     * @throws IOException if an I/O error occurs
     * @throws InterruptedException if the operation is interrupted
     */
    public ApiResponse<PointDto> updatePointPosition(String pointId, String position) throws IOException, InterruptedException {
        return authenticatedPut(POINTS_ENDPOINT + "/" + pointId + "/position", position, PointDto.class);
    }
}
