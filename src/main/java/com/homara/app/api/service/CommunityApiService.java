package com.homara.app.api.service;

import com.homara.app.api.client.CommunityApiClient;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.community.CommunityDto;
import com.homara.app.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for community-related API operations.
 */
@Service
public class CommunityApiService {
    
    private final CommunityApiClient apiClient;
    private final CacheManager cacheManager;
    
    /**
     * Creates a new CommunityApiService with the specified dependencies.
     *
     * @param apiClient the community API client
     * @param cacheManager the cache manager
     */
    @Autowired
    public CommunityApiService(CommunityApiClient apiClient, CacheManager cacheManager) {
        this.apiClient = apiClient;
        this.cacheManager = cacheManager;
    }
    
    /**
     * Gets all communities.
     *
     * @return a list of all communities
     */
    public CompletableFuture<ApiResponse<List<CommunityDto>>> getAllCommunities() {
        String cacheKey = "communities:all";
        
        // Try to get from cache first
        List<CommunityDto> cachedCommunities = cacheManager.get(cacheKey);
        if (cachedCommunities != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedCommunities));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<CommunityDto>> response = apiClient.getAllCommunities();
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get communities: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets a community by ID.
     *
     * @param communityId the ID of the community to get
     * @return the community with the specified ID
     */
    public CompletableFuture<ApiResponse<CommunityDto>> getCommunityById(String communityId) {
        String cacheKey = "communities:" + communityId;
        
        // Try to get from cache first
        CommunityDto cachedCommunity = cacheManager.get(cacheKey);
        if (cachedCommunity != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedCommunity));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<CommunityDto> response = apiClient.getCommunityById(communityId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get community: " + e.getMessage());
            }
        });
    }
    
    /**
     * Creates a new community.
     *
     * @param community the community to create
     * @return the created community
     */
    public CompletableFuture<ApiResponse<CommunityDto>> createCommunity(CommunityDto community) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<CommunityDto> response = apiClient.createCommunity(community);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("communities:all");
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to create community: " + e.getMessage());
            }
        });
    }
    
    /**
     * Updates an existing community.
     *
     * @param communityId the ID of the community to update
     * @param community the updated community data
     * @return the updated community
     */
    public CompletableFuture<ApiResponse<CommunityDto>> updateCommunity(String communityId, CommunityDto community) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<CommunityDto> response = apiClient.updateCommunity(communityId, community);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("communities:all");
                    cacheManager.invalidate("communities:" + communityId);
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to update community: " + e.getMessage());
            }
        });
    }
    
    /**
     * Deletes a community.
     *
     * @param communityId the ID of the community to delete
     * @return the response indicating success or failure
     */
    public CompletableFuture<ApiResponse<Void>> deleteCommunity(String communityId) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<Void> response = apiClient.deleteCommunity(communityId);
                
                // Invalidate cache if successful
                if (response.isSuccess()) {
                    cacheManager.invalidate("communities:all");
                    cacheManager.invalidate("communities:" + communityId);
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to delete community: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets all communities for a user.
     *
     * @param userId the ID of the user
     * @return a list of communities for the user
     */
    public CompletableFuture<ApiResponse<List<CommunityDto>>> getUserCommunities(String userId) {
        String cacheKey = "communities:user:" + userId;
        
        // Try to get from cache first
        List<CommunityDto> cachedCommunities = cacheManager.get(cacheKey);
        if (cachedCommunities != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedCommunities));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<CommunityDto>> response = apiClient.getUserCommunities(userId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get user communities: " + e.getMessage());
            }
        });
    }
}
