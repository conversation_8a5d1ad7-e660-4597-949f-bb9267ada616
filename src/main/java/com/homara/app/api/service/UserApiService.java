package com.homara.app.api.service;

import com.homara.app.api.client.UserApiClient;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.user.UserDto;
import com.homara.app.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for user-related API operations.
 */
@Service
public class UserApiService {
    
    private final UserApiClient apiClient;
    private final CacheManager cacheManager;
    
    /**
     * Creates a new UserApiService with the specified dependencies.
     *
     * @param apiClient the user API client
     * @param cacheManager the cache manager
     */
    @Autowired
    public UserApiService(UserApiClient apiClient, CacheManager cacheManager) {
        this.apiClient = apiClient;
        this.cacheManager = cacheManager;
    }
    
    /**
     * Gets all users.
     *
     * @return a list of all users
     */
    public CompletableFuture<ApiResponse<List<UserDto>>> getAllUsers() {
        String cacheKey = "users:all";
        
        // Try to get from cache first
        List<UserDto> cachedUsers = cacheManager.get(cacheKey);
        if (cachedUsers != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedUsers));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<UserDto>> response = apiClient.getAllUsers();
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get users: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets a user by ID.
     *
     * @param userId the ID of the user to get
     * @return the user with the specified ID
     */
    public CompletableFuture<ApiResponse<UserDto>> getUserById(String userId) {
        String cacheKey = "users:" + userId;
        
        // Try to get from cache first
        UserDto cachedUser = cacheManager.get(cacheKey);
        if (cachedUser != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedUser));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<UserDto> response = apiClient.getUserById(userId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get user: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets a user by email.
     *
     * @param email the email of the user to get
     * @return the user with the specified email
     */
    public CompletableFuture<ApiResponse<UserDto>> getUserByEmail(String email) {
        String cacheKey = "users:email:" + email;
        
        // Try to get from cache first
        UserDto cachedUser = cacheManager.get(cacheKey);
        if (cachedUser != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedUser));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<UserDto> response = apiClient.getUserByEmail(email);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                    // Also cache by ID for future reference
                    if (response.getData().getId() != null) {
                        cacheManager.put("users:" + response.getData().getId(), response.getData());
                    }
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get user by email: " + e.getMessage());
            }
        });
    }
    
    /**
     * Creates a new user.
     *
     * @param user the user to create
     * @return the created user
     */
    public CompletableFuture<ApiResponse<UserDto>> createUser(UserDto user) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<UserDto> response = apiClient.createUser(user);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("users:all");
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to create user: " + e.getMessage());
            }
        });
    }
    
    /**
     * Updates an existing user.
     *
     * @param userId the ID of the user to update
     * @param user the updated user data
     * @return the updated user
     */
    public CompletableFuture<ApiResponse<UserDto>> updateUser(String userId, UserDto user) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<UserDto> response = apiClient.updateUser(userId, user);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("users:all");
                    cacheManager.invalidate("users:" + userId);
                    if (user.getEmail() != null) {
                        cacheManager.invalidate("users:email:" + user.getEmail());
                    }
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to update user: " + e.getMessage());
            }
        });
    }
    
    /**
     * Deletes a user.
     *
     * @param userId the ID of the user to delete
     * @return the response indicating success or failure
     */
    public CompletableFuture<ApiResponse<Void>> deleteUser(String userId) {
        // First get the user to know which caches to invalidate
        return getUserById(userId).thenCompose(userResponse -> {
            if (!userResponse.isSuccess() || userResponse.getData() == null) {
                return CompletableFuture.completedFuture(ApiResponse.error("Failed to get user for deletion"));
            }
            
            UserDto user = userResponse.getData();
            
            return CompletableFuture.supplyAsync(() -> {
                try {
                    ApiResponse<Void> response = apiClient.deleteUser(userId);
                    
                    // Invalidate cache if successful
                    if (response.isSuccess()) {
                        cacheManager.invalidate("users:all");
                        cacheManager.invalidate("users:" + userId);
                        if (user.getEmail() != null) {
                            cacheManager.invalidate("users:email:" + user.getEmail());
                        }
                    }
                    
                    return response;
                } catch (IOException | InterruptedException e) {
                    return ApiResponse.error("Failed to delete user: " + e.getMessage());
                }
            });
        });
    }
    
    /**
     * Gets all users in a community.
     *
     * @param communityId the ID of the community
     * @return a list of users in the community
     */
    public CompletableFuture<ApiResponse<List<UserDto>>> getCommunityUsers(String communityId) {
        String cacheKey = "users:community:" + communityId;
        
        // Try to get from cache first
        List<UserDto> cachedUsers = cacheManager.get(cacheKey);
        if (cachedUsers != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedUsers));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<UserDto>> response = apiClient.getCommunityUsers(communityId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get community users: " + e.getMessage());
            }
        });
    }
}
