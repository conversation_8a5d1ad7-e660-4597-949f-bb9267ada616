package com.homara.app.api.service;

import com.homara.app.api.client.PointApiClient;
import com.homara.app.api.model.ApiResponse;
import com.homara.app.api.model.point.PointDto;
import com.homara.app.cache.CacheManager;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

/**
 * Service for point-related API operations.
 */
@Service
public class PointApiService {
    
    private final PointApiClient apiClient;
    private final CacheManager cacheManager;
    
    /**
     * Creates a new PointApiService with the specified dependencies.
     *
     * @param apiClient the point API client
     * @param cacheManager the cache manager
     */
    @Autowired
    public PointApiService(PointApiClient apiClient, CacheManager cacheManager) {
        this.apiClient = apiClient;
        this.cacheManager = cacheManager;
    }
    
    /**
     * Gets all points.
     *
     * @return a list of all points
     */
    public CompletableFuture<ApiResponse<List<PointDto>>> getAllPoints() {
        String cacheKey = "points:all";
        
        // Try to get from cache first
        List<PointDto> cachedPoints = cacheManager.get(cacheKey);
        if (cachedPoints != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedPoints));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<PointDto>> response = apiClient.getAllPoints();
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get points: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets a point by ID.
     *
     * @param pointId the ID of the point to get
     * @return the point with the specified ID
     */
    public CompletableFuture<ApiResponse<PointDto>> getPointById(String pointId) {
        String cacheKey = "points:" + pointId;
        
        // Try to get from cache first
        PointDto cachedPoint = cacheManager.get(cacheKey);
        if (cachedPoint != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedPoint));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<PointDto> response = apiClient.getPointById(pointId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get point: " + e.getMessage());
            }
        });
    }
    
    /**
     * Creates a new point.
     *
     * @param point the point to create
     * @return the created point
     */
    public CompletableFuture<ApiResponse<PointDto>> createPoint(PointDto point) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<PointDto> response = apiClient.createPoint(point);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("points:all");
                    cacheManager.invalidate("points:community:" + point.getCommunityId());
                    cacheManager.invalidate("points:user:" + point.getUserId());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to create point: " + e.getMessage());
            }
        });
    }
    
    /**
     * Updates an existing point.
     *
     * @param pointId the ID of the point to update
     * @param point the updated point data
     * @return the updated point
     */
    public CompletableFuture<ApiResponse<PointDto>> updatePoint(String pointId, PointDto point) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<PointDto> response = apiClient.updatePoint(pointId, point);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.invalidate("points:all");
                    cacheManager.invalidate("points:" + pointId);
                    cacheManager.invalidate("points:community:" + point.getCommunityId());
                    cacheManager.invalidate("points:user:" + point.getUserId());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to update point: " + e.getMessage());
            }
        });
    }
    
    /**
     * Deletes a point.
     *
     * @param pointId the ID of the point to delete
     * @return the response indicating success or failure
     */
    public CompletableFuture<ApiResponse<Void>> deletePoint(String pointId) {
        // First get the point to know which caches to invalidate
        return getPointById(pointId).thenCompose(pointResponse -> {
            if (!pointResponse.isSuccess() || pointResponse.getData() == null) {
                return CompletableFuture.completedFuture(ApiResponse.error("Failed to get point for deletion"));
            }
            
            PointDto point = pointResponse.getData();
            
            return CompletableFuture.supplyAsync(() -> {
                try {
                    ApiResponse<Void> response = apiClient.deletePoint(pointId);
                    
                    // Invalidate cache if successful
                    if (response.isSuccess()) {
                        cacheManager.invalidate("points:all");
                        cacheManager.invalidate("points:" + pointId);
                        cacheManager.invalidate("points:community:" + point.getCommunityId());
                        cacheManager.invalidate("points:user:" + point.getUserId());
                    }
                    
                    return response;
                } catch (IOException | InterruptedException e) {
                    return ApiResponse.error("Failed to delete point: " + e.getMessage());
                }
            });
        });
    }
    
    /**
     * Gets all points for a community.
     *
     * @param communityId the ID of the community
     * @return a list of points for the community
     */
    public CompletableFuture<ApiResponse<List<PointDto>>> getCommunityPoints(String communityId) {
        String cacheKey = "points:community:" + communityId;
        
        // Try to get from cache first
        List<PointDto> cachedPoints = cacheManager.get(cacheKey);
        if (cachedPoints != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedPoints));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<PointDto>> response = apiClient.getCommunityPoints(communityId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get community points: " + e.getMessage());
            }
        });
    }
    
    /**
     * Gets all points for a user.
     *
     * @param userId the ID of the user
     * @return a list of points for the user
     */
    public CompletableFuture<ApiResponse<List<PointDto>>> getUserPoints(String userId) {
        String cacheKey = "points:user:" + userId;
        
        // Try to get from cache first
        List<PointDto> cachedPoints = cacheManager.get(cacheKey);
        if (cachedPoints != null) {
            return CompletableFuture.completedFuture(ApiResponse.success(cachedPoints));
        }
        
        // If not in cache, fetch from API
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<List<PointDto>> response = apiClient.getUserPoints(userId);
                
                // Cache the result if successful
                if (response.isSuccess() && response.getData() != null) {
                    cacheManager.put(cacheKey, response.getData());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to get user points: " + e.getMessage());
            }
        });
    }
    
    /**
     * Updates the position of a point.
     *
     * @param pointId the ID of the point to update
     * @param position the new position
     * @return the updated point
     */
    public CompletableFuture<ApiResponse<PointDto>> updatePointPosition(String pointId, String position) {
        return CompletableFuture.supplyAsync(() -> {
            try {
                ApiResponse<PointDto> response = apiClient.updatePointPosition(pointId, position);
                
                // Invalidate cache if successful
                if (response.isSuccess() && response.getData() != null) {
                    PointDto point = response.getData();
                    cacheManager.invalidate("points:all");
                    cacheManager.invalidate("points:" + pointId);
                    cacheManager.invalidate("points:community:" + point.getCommunityId());
                    cacheManager.invalidate("points:user:" + point.getUserId());
                }
                
                return response;
            } catch (IOException | InterruptedException e) {
                return ApiResponse.error("Failed to update point position: " + e.getMessage());
            }
        });
    }
}
