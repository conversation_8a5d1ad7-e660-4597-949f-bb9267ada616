package com.homara.app.api.model.user;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.homara.app.api.model.community.CommunityDto;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * Data Transfer Object for User.
 * This class is used for API requests and responses.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class UserDto {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("communityId")
    private String communityId;
    
    @JsonProperty("pointId")
    private String pointId;
    
    @JsonProperty("pointPosition")
    private String pointPosition;
    
    @JsonProperty("username")
    private String username;
    
    @JsonProperty("email")
    private String email;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("communities")
    private List<CommunityDto> communities = new ArrayList<>();
    
    /**
     * Default constructor for Jackson deserialization.
     */
    public UserDto() {
    }
    
    /**
     * Creates a new UserDto with the specified properties.
     *
     * @param id the user's database ID
     * @param communityId the community ID (CommunityID in MongoDB)
     * @param pointId the point ID (PointID in MongoDB)
     * @param pointPosition the point position
     * @param username the username
     * @param email the email
     * @param name the name
     */
    public UserDto(String id, String communityId, String pointId, String pointPosition,
                  String username, String email, String name) {
        this.id = id;
        this.communityId = communityId;
        this.pointId = pointId;
        this.pointPosition = pointPosition;
        this.username = username;
        this.email = email;
        this.name = name;
    }
    
    /**
     * Creates a new UserDto with the specified properties (without database ID).
     *
     * @param communityId the community ID (CommunityID in MongoDB)
     * @param pointId the point ID (PointID in MongoDB)
     * @param pointPosition the point position
     * @param username the username
     * @param email the email
     * @param name the name
     */
    public UserDto(String communityId, String pointId, String pointPosition,
                  String username, String email, String name) {
        this.communityId = communityId;
        this.pointId = pointId;
        this.pointPosition = pointPosition;
        this.username = username;
        this.email = email;
        this.name = name;
    }
    
    /**
     * Gets the user's database ID.
     *
     * @return the user's database ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Sets the user's database ID.
     *
     * @param id the user's database ID
     */
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * Gets the community ID (CommunityID in MongoDB).
     *
     * @return the community ID
     */
    public String getCommunityId() {
        return communityId;
    }
    
    /**
     * Sets the community ID (CommunityID in MongoDB).
     *
     * @param communityId the community ID
     */
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    
    /**
     * Gets the point ID (PointID in MongoDB).
     *
     * @return the point ID
     */
    public String getPointId() {
        return pointId;
    }
    
    /**
     * Sets the point ID (PointID in MongoDB).
     *
     * @param pointId the point ID
     */
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    /**
     * Gets the point position.
     *
     * @return the point position
     */
    public String getPointPosition() {
        return pointPosition;
    }
    
    /**
     * Sets the point position.
     *
     * @param pointPosition the point position
     */
    public void setPointPosition(String pointPosition) {
        this.pointPosition = pointPosition;
    }
    
    /**
     * Gets the username.
     *
     * @return the username
     */
    public String getUsername() {
        return username;
    }
    
    /**
     * Sets the username.
     *
     * @param username the username
     */
    public void setUsername(String username) {
        this.username = username;
    }
    
    /**
     * Gets the email.
     *
     * @return the email
     */
    public String getEmail() {
        return email;
    }
    
    /**
     * Sets the email.
     *
     * @param email the email
     */
    public void setEmail(String email) {
        this.email = email;
    }
    
    /**
     * Gets the name.
     *
     * @return the name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Sets the name.
     *
     * @param name the name
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * Gets the communities.
     *
     * @return the communities
     */
    public List<CommunityDto> getCommunities() {
        return communities;
    }
    
    /**
     * Sets the communities.
     *
     * @param communities the communities
     */
    public void setCommunities(List<CommunityDto> communities) {
        this.communities = communities;
    }
    
    /**
     * Adds a community.
     *
     * @param community the community to add
     */
    public void addCommunity(CommunityDto community) {
        this.communities.add(community);
    }
    
    /**
     * Converts this DTO to a domain model object.
     *
     * @return a User domain model object
     */
    public com.homara.app.mongodb.model.User toDomainModel() {
        com.homara.app.mongodb.model.User user = new com.homara.app.mongodb.model.User();
        user.setId(this.id);
        user.setCommunityID(this.communityId);
        user.setPointID(this.pointId);
        user.setPointPosition(this.pointPosition);
        user.setUsername(this.username);
        user.setEmail(this.email);
        user.setName(this.name);
        
        if (this.communities != null && !this.communities.isEmpty()) {
            List<com.homara.app.mongodb.model.Community> domainCommunities = this.communities.stream()
                    .map(CommunityDto::toDomainModel)
                    .collect(Collectors.toList());
            user.setCommunities(domainCommunities);
        }
        
        return user;
    }
    
    /**
     * Creates a DTO from a domain model object.
     *
     * @param user the domain model object
     * @return a UserDto
     */
    public static UserDto fromDomainModel(com.homara.app.mongodb.model.User user) {
        UserDto dto = new UserDto(
                user.getId(),
                user.getCommunityID(),
                user.getPointID(),
                user.getPointPosition(),
                user.getUsername(),
                user.getEmail(),
                user.getName()
        );
        
        if (user.getCommunities() != null && !user.getCommunities().isEmpty()) {
            List<CommunityDto> dtoCommunities = user.getCommunities().stream()
                    .map(CommunityDto::fromDomainModel)
                    .collect(Collectors.toList());
            dto.setCommunities(dtoCommunities);
        }
        
        return dto;
    }
}
