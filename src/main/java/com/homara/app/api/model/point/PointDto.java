package com.homara.app.api.model.point;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data Transfer Object for Point.
 * This class is used for API requests and responses.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class PointDto {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("pointId")
    private String pointId;
    
    @JsonProperty("communityId")
    private String communityId;
    
    @JsonProperty("userId")
    private String userId;
    
    @JsonProperty("position")
    private String position;
    
    @JsonProperty("name")
    private String name;
    
    /**
     * Default constructor for Jackson deserialization.
     */
    public PointDto() {
    }
    
    /**
     * Creates a new PointDto with the specified properties.
     *
     * @param id the point's database ID
     * @param pointId the point ID (PointID in MongoDB)
     * @param communityId the community ID
     * @param userId the user ID
     * @param position the point position
     * @param name the point name
     */
    public PointDto(String id, String pointId, String communityId, String userId, String position, String name) {
        this.id = id;
        this.pointId = pointId;
        this.communityId = communityId;
        this.userId = userId;
        this.position = position;
        this.name = name;
    }
    
    /**
     * Creates a new PointDto with the specified properties (without database ID).
     *
     * @param pointId the point ID (PointID in MongoDB)
     * @param communityId the community ID
     * @param userId the user ID
     * @param position the point position
     * @param name the point name
     */
    public PointDto(String pointId, String communityId, String userId, String position, String name) {
        this.pointId = pointId;
        this.communityId = communityId;
        this.userId = userId;
        this.position = position;
        this.name = name;
    }
    
    /**
     * Gets the point's database ID.
     *
     * @return the point's database ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Sets the point's database ID.
     *
     * @param id the point's database ID
     */
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * Gets the point ID (PointID in MongoDB).
     *
     * @return the point ID
     */
    public String getPointId() {
        return pointId;
    }
    
    /**
     * Sets the point ID (PointID in MongoDB).
     *
     * @param pointId the point ID
     */
    public void setPointId(String pointId) {
        this.pointId = pointId;
    }
    
    /**
     * Gets the community ID.
     *
     * @return the community ID
     */
    public String getCommunityId() {
        return communityId;
    }
    
    /**
     * Sets the community ID.
     *
     * @param communityId the community ID
     */
    public void setCommunityId(String communityId) {
        this.communityId = communityId;
    }
    
    /**
     * Gets the user ID.
     *
     * @return the user ID
     */
    public String getUserId() {
        return userId;
    }
    
    /**
     * Sets the user ID.
     *
     * @param userId the user ID
     */
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    /**
     * Gets the point position.
     *
     * @return the point position
     */
    public String getPosition() {
        return position;
    }
    
    /**
     * Sets the point position.
     *
     * @param position the point position
     */
    public void setPosition(String position) {
        this.position = position;
    }
    
    /**
     * Gets the point name.
     *
     * @return the point name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Sets the point name.
     *
     * @param name the point name
     */
    public void setName(String name) {
        this.name = name;
    }
}
