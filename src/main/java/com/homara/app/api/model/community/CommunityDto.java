package com.homara.app.api.model.community;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Data Transfer Object for Community.
 * This class is used for API requests and responses.
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class CommunityDto {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("name")
    private String name;
    
    @JsonProperty("description")
    private String description;
    
    @JsonProperty("userRole")
    private String userRole;
    
    /**
     * Default constructor for Jackson deserialization.
     */
    public CommunityDto() {
    }
    
    /**
     * Creates a new CommunityDto with the specified properties.
     *
     * @param id the community ID
     * @param name the community name
     * @param description the community description
     * @param userRole the user's role in the community
     */
    public CommunityDto(String id, String name, String description, String userRole) {
        this.id = id;
        this.name = name;
        this.description = description;
        this.userRole = userRole;
    }
    
    /**
     * Creates a new CommunityDto with the specified properties (without ID).
     *
     * @param name the community name
     * @param description the community description
     * @param userRole the user's role in the community
     */
    public CommunityDto(String name, String description, String userRole) {
        this.name = name;
        this.description = description;
        this.userRole = userRole;
    }
    
    /**
     * Gets the community ID.
     *
     * @return the community ID
     */
    public String getId() {
        return id;
    }
    
    /**
     * Sets the community ID.
     *
     * @param id the community ID
     */
    public void setId(String id) {
        this.id = id;
    }
    
    /**
     * Gets the community name.
     *
     * @return the community name
     */
    public String getName() {
        return name;
    }
    
    /**
     * Sets the community name.
     *
     * @param name the community name
     */
    public void setName(String name) {
        this.name = name;
    }
    
    /**
     * Gets the community description.
     *
     * @return the community description
     */
    public String getDescription() {
        return description;
    }
    
    /**
     * Sets the community description.
     *
     * @param description the community description
     */
    public void setDescription(String description) {
        this.description = description;
    }
    
    /**
     * Gets the user's role in the community.
     *
     * @return the user's role
     */
    public String getUserRole() {
        return userRole;
    }
    
    /**
     * Sets the user's role in the community.
     *
     * @param userRole the user's role
     */
    public void setUserRole(String userRole) {
        this.userRole = userRole;
    }
    
    /**
     * Converts this DTO to a domain model object.
     *
     * @return a Community domain model object
     */
    public com.homara.app.mongodb.model.Community toDomainModel() {
        com.homara.app.mongodb.model.Community community = new com.homara.app.mongodb.model.Community();
        community.setId(this.id);
        community.setName(this.name);
        community.setDescription(this.description);
        community.setUserRole(this.userRole);
        return community;
    }
    
    /**
     * Creates a DTO from a domain model object.
     *
     * @param community the domain model object
     * @return a CommunityDto
     */
    public static CommunityDto fromDomainModel(com.homara.app.mongodb.model.Community community) {
        return new CommunityDto(
                community.getId(),
                community.getName(),
                community.getDescription(),
                community.getUserRole()
        );
    }
}
