package com.homara.app.api.model;

/**
 * Generic API response wrapper for all API calls.
 *
 * @param <T> the type of data contained in the response
 */
public class ApiResponse<T> {
    
    private final boolean success;
    private final String message;
    private final T data;
    
    /**
     * Creates a new API response.
     *
     * @param success whether the request was successful
     * @param message error message if the request failed, null otherwise
     * @param data the response data if the request was successful, null otherwise
     */
    public ApiResponse(boolean success, String message, T data) {
        this.success = success;
        this.message = message;
        this.data = data;
    }
    
    /**
     * Creates a successful API response with the given data.
     *
     * @param data the response data
     * @param <T> the type of the response data
     * @return a successful API response
     */
    public static <T> ApiResponse<T> success(T data) {
        return new ApiResponse<>(true, null, data);
    }
    
    /**
     * Creates a failed API response with the given error message.
     *
     * @param message the error message
     * @param <T> the type of the response data
     * @return a failed API response
     */
    public static <T> ApiResponse<T> error(String message) {
        return new ApiResponse<>(false, message, null);
    }
    
    /**
     * Returns whether the request was successful.
     *
     * @return true if the request was successful, false otherwise
     */
    public boolean isSuccess() {
        return success;
    }
    
    /**
     * Returns the error message if the request failed.
     *
     * @return the error message, or null if the request was successful
     */
    public String getMessage() {
        return message;
    }
    
    /**
     * Returns the response data if the request was successful.
     *
     * @return the response data, or null if the request failed
     */
    public T getData() {
        return data;
    }
}
