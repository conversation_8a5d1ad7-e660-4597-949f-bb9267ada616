package com.homara.app.api;

import com.homara.app.api.client.ApiClient;
import com.homara.app.api.client.CommunityApiClient;
import com.homara.app.api.client.PointApiClient;
import com.homara.app.api.client.UserApiClient;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * Configuration class for API clients.
 */
@Configuration
public class ApiConfig {
    
    @Value("${api.base-url:https://api.homara.community}")
    private String apiBaseUrl;
    
    /**
     * Creates a new ApiClient bean.
     *
     * @return the ApiClient
     */
    @Bean
    public ApiClient apiClient() {
        return new ApiClient(apiBaseUrl);
    }
    
    /**
     * Creates a new CommunityApiClient bean.
     *
     * @return the CommunityApiClient
     */
    @Bean
    public CommunityApiClient communityApiClient() {
        return new CommunityApiClient(apiBaseUrl);
    }
    
    /**
     * Creates a new PointApiClient bean.
     *
     * @return the PointApiClient
     */
    @Bean
    public PointApiClient pointApiClient() {
        return new PointApiClient(apiBaseUrl);
    }
    
    /**
     * Creates a new UserApiClient bean.
     *
     * @return the UserApiClient
     */
    @Bean
    public UserApiClient userApiClient() {
        return new UserApiClient(apiBaseUrl);
    }
}
