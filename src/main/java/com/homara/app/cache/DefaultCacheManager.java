package com.homara.app.cache;

import com.homara.app.cache.local.InMemoryCache;
import com.homara.app.cache.local.LocalCache;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Default implementation of the CacheManager interface.
 */
@Component
public class DefaultCacheManager implements CacheManager {
    
    private final LocalCache localCache;
    private final SyncStrategy syncStrategy;
    
    /**
     * Creates a new DefaultCacheManager with the specified dependencies.
     *
     * @param syncStrategy the synchronization strategy
     */
    @Autowired
    public DefaultCacheManager(SyncStrategy syncStrategy) {
        this.localCache = new InMemoryCache();
        this.syncStrategy = syncStrategy;
    }
    
    @Override
    public <T> T get(String key) {
        return localCache.get(key);
    }
    
    @Override
    public <T> void put(String key, T value) {
        localCache.put(key, value);
    }
    
    @Override
    public <T> void put(String key, T value, long ttlSeconds) {
        localCache.put(key, value, ttlSeconds);
    }
    
    @Override
    public void invalidate(String key) {
        localCache.remove(key);
    }
    
    @Override
    public void invalidateAll() {
        localCache.clear();
    }
    
    @Override
    public void sync() {
        syncStrategy.sync();
    }
    
    /**
     * Synchronizes a specific cache key with the backend.
     *
     * @param key the cache key to synchronize
     */
    public void syncKey(String key) {
        syncStrategy.syncKey(key);
    }
    
    /**
     * Gets the size of the cache.
     *
     * @return the number of entries in the cache
     */
    public int size() {
        return localCache.size();
    }
    
    /**
     * Checks if the cache contains a key.
     *
     * @param key the cache key
     * @return true if the cache contains the key, false otherwise
     */
    public boolean containsKey(String key) {
        return localCache.containsKey(key);
    }
    
    /**
     * Shuts down the cache manager.
     */
    public void shutdown() {
        if (localCache instanceof InMemoryCache) {
            ((InMemoryCache) localCache).shutdown();
        }
    }
}
