package com.homara.app.cache.local;

import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * In-memory implementation of the LocalCache interface.
 */
public class InMemoryCache implements LocalCache {

    private final Map<String, CacheEntry<?>> cache = new ConcurrentHashMap<>();
    private final ScheduledExecutorService scheduler = Executors.newScheduledThreadPool(1);

    /**
     * Creates a new InMemoryCache.
     */
    public InMemoryCache() {
        // Schedule a task to clean up expired entries every minute
        scheduler.scheduleAtFixedRate(this::cleanupExpiredEntries, 1, 1, TimeUnit.MINUTES);
    }

    @Override
    public <T> T get(String key) {
        CacheEntry<?> entry = cache.get(key);

        if (entry == null) {
            return null;
        }

        // Check if the entry has expired
        if (entry.isExpired()) {
            cache.remove(key);
            return null;
        }

        @SuppressWarnings("unchecked")
        T result = (T) entry.getValue();
        return result;
    }

    @Override
    public <T> void put(String key, T value) {
        // Default TTL: 1 hour
        put(key, value, 3600);
    }

    @Override
    public <T> void put(String key, T value, long ttlSeconds) {
        long expirationTime = ttlSeconds > 0 ? System.currentTimeMillis() + (ttlSeconds * 1000) : 0;
        cache.put(key, new CacheEntry<>(value, expirationTime));
    }

    @Override
    public void remove(String key) {
        cache.remove(key);
    }

    @Override
    public void clear() {
        cache.clear();
    }

    @Override
    public int size() {
        return cache.size();
    }

    @Override
    public boolean containsKey(String key) {
        return cache.containsKey(key) && !cache.get(key).isExpired();
    }

    /**
     * Cleans up expired entries from the cache.
     */
    private void cleanupExpiredEntries() {
        long now = System.currentTimeMillis();
        cache.entrySet().removeIf(entry -> entry.getValue().getExpirationTime() > 0 && entry.getValue().getExpirationTime() <= now);
    }

    /**
     * Shuts down the scheduler.
     */
    public void shutdown() {
        scheduler.shutdown();
    }

    /**
     * Cache entry with expiration time.
     *
     * @param <T> the type of the value
     */
    private static class CacheEntry<T> {
        private final T value;
        private final long expirationTime;

        /**
         * Creates a new CacheEntry.
         *
         * @param value the value to cache
         * @param expirationTime the expiration time in milliseconds since epoch, or 0 for no expiration
         */
        public CacheEntry(T value, long expirationTime) {
            this.value = value;
            this.expirationTime = expirationTime;
        }

        /**
         * Gets the cached value.
         *
         * @return the cached value
         */
        public T getValue() {
            return value;
        }

        /**
         * Gets the expiration time.
         *
         * @return the expiration time in milliseconds since epoch, or 0 for no expiration
         */
        public long getExpirationTime() {
            return expirationTime;
        }

        /**
         * Checks if the entry has expired.
         *
         * @return true if the entry has expired, false otherwise
         */
        public boolean isExpired() {
            return expirationTime > 0 && System.currentTimeMillis() > expirationTime;
        }
    }
}
