package com.homara.app.cache.local;

/**
 * Interface for local cache implementations.
 */
public interface LocalCache {
    
    /**
     * Gets a value from the cache.
     *
     * @param key the cache key
     * @param <T> the type of the value
     * @return the cached value, or null if not found
     */
    <T> T get(String key);
    
    /**
     * Puts a value in the cache.
     *
     * @param key the cache key
     * @param value the value to cache
     * @param <T> the type of the value
     */
    <T> void put(String key, T value);
    
    /**
     * Puts a value in the cache with a specific time-to-live.
     *
     * @param key the cache key
     * @param value the value to cache
     * @param ttlSeconds the time-to-live in seconds
     * @param <T> the type of the value
     */
    <T> void put(String key, T value, long ttlSeconds);
    
    /**
     * Removes a value from the cache.
     *
     * @param key the cache key
     */
    void remove(String key);
    
    /**
     * Clears all values from the cache.
     */
    void clear();
    
    /**
     * Gets the size of the cache.
     *
     * @return the number of entries in the cache
     */
    int size();
    
    /**
     * Checks if the cache contains a key.
     *
     * @param key the cache key
     * @return true if the cache contains the key, false otherwise
     */
    boolean containsKey(String key);
}
