package com.homara.app.cache;

import org.springframework.stereotype.Component;

/**
 * Default implementation of the SyncStrategy interface.
 * This implementation does nothing by default, but can be extended to implement
 * actual synchronization logic.
 */
@Component
public class DefaultSyncStrategy implements SyncStrategy {
    
    @Override
    public void sync() {
        // Default implementation does nothing
        // This can be extended to implement actual synchronization logic
    }
    
    @Override
    public void syncKey(String key) {
        // Default implementation does nothing
        // This can be extended to implement actual synchronization logic
    }
}
