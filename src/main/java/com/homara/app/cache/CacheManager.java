package com.homara.app.cache;

/**
 * Interface for cache management.
 */
public interface CacheManager {
    
    /**
     * Gets a value from the cache.
     *
     * @param key the cache key
     * @param <T> the type of the value
     * @return the cached value, or null if not found
     */
    <T> T get(String key);
    
    /**
     * Puts a value in the cache.
     *
     * @param key the cache key
     * @param value the value to cache
     * @param <T> the type of the value
     */
    <T> void put(String key, T value);
    
    /**
     * Puts a value in the cache with a specific time-to-live.
     *
     * @param key the cache key
     * @param value the value to cache
     * @param ttlSeconds the time-to-live in seconds
     * @param <T> the type of the value
     */
    <T> void put(String key, T value, long ttlSeconds);
    
    /**
     * Invalidates a cache entry.
     *
     * @param key the cache key to invalidate
     */
    void invalidate(String key);
    
    /**
     * Invalidates all cache entries.
     */
    void invalidateAll();
    
    /**
     * Synchronizes the cache with the backend.
     */
    void sync();
}
