<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #2d2d2d;
            height: 100vh;
            display: flex;
            flex-direction: column;
            color: #ffffff;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
        }

        .header-content {
            display: flex;
            align-items: center;
            padding: 0 20px;
            position: relative;
        }

        .header-left {
            flex: 1;
        }

        .header-center {
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            text-align: center;
        }

        .header-center h1 {
            margin: 0;
            font-size: 32px;
            font-weight: 700;
            color: #ffffff;
        }

        .logout-button {
            background-color: #000000;
            color: #ffffff;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }

        .logout-button:hover {
            background-color: #1a1a1a;
        }

        /* Tab Navigation */
        .tab-container {
            background-color: #3d3d3d;
            border-bottom: 1px solid #555;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 0 30px;
        }

        .tab-welcome {
            color: #ffffff;
            font-size: 18px;
            font-weight: 600;
        }

        .tab-nav {
            display: flex;
            list-style: none;
            margin: 0;
            padding: 0;
        }

        .tab-nav li {
            margin-left: 20px;
        }

        .tab-nav button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            text-transform: uppercase;
        }

        .tab-nav button:hover {
            background-color: #1a1a1a;
            color: #ffffff;
        }

        .tab-nav button.active {
            color: #ffffff;
            border-bottom-color: #000000;
            background-color: #1a1a1a;
        }

        /* Tab Content */
        .tab-content {
            flex: 1;
            overflow-y: auto;
            padding: 30px;
        }

        /* Override padding for home tab only */
        #home {
            margin-left: -30px;
            padding-left: 0;
        }

        .tab-pane {
            display: none;
            max-width: 1200px;
            margin: 0 auto;
        }

        .tab-pane.active {
            display: block;
        }

        .tab-pane h2 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #ffffff;
        }

        .tab-pane .description {
            font-size: 18px;
            color: #ffffff;
            margin-bottom: 30px;
        }

        /* Cards */
        .card {
            background: #3d3d3d;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            margin-bottom: 20px;
            border: 1px solid #555;
        }

        .card h3 {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #ffffff;
        }

        .card p {
            color: #ffffff;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 5px 10px 5px 0;
        }

        .btn-primary {
            background-color: #000000;
            color: #ffffff;
        }

        .btn-primary:hover {
            background-color: #1a1a1a;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: #000000;
            color: #ffffff;
        }

        .btn-success:hover {
            background-color: #1a1a1a;
            transform: translateY(-2px);
        }

        .btn-warning {
            background-color: #000000;
            color: #ffffff;
        }

        .btn-warning:hover {
            background-color: #1a1a1a;
            transform: translateY(-2px);
        }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #3d3d3d;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            text-align: center;
            border: 1px solid #555;
        }

        .stat-card .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .stat-card .stat-label {
            color: #ffffff;
            font-size: 14px;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        /* Communities Section Styles */
        .communities-section {
            margin: 0;
            padding: 40px 0;
        }

        .communities-table {
            background: #3d3d3d;
            border-radius: 12px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
            border: 1px solid #555;
            overflow: visible;
            width: fit-content;
            margin: 0;
            margin-right: 30px;
        }

        .table-header {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
        }

        .table-row {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
            border-bottom: 1px solid #555;
            position: relative;
        }

        .table-row:last-child {
            border-bottom: none;
        }

        .table-row:hover {
            background-color: #2a2a2a;
        }

        .founder-tab {
            position: absolute;
            right: -24px;
            top: 25%;
            height: 50%;
            width: 24px;
            background-color: #000000;
            border-radius: 0 4px 4px 0;
        }

        .table-cell {
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            text-align: center;
            border-right: 1px solid #555;
            border-top: 1px solid #555;
            border-bottom: 1px solid #555;
            background-color: #3d3d3d;
            transition: all 0.3s ease;
            position: relative;
        }

        .table-cell:first-child {
            border-left: 1px solid #555;
        }

        .table-cell:last-child {
            border-right: 1px solid #555;
        }

        .table-row .table-cell:hover {
            border: 1px solid #000000;
            background-color: #3d3d3d;
            transform: translateY(-2px);
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            z-index: 1;
        }

        .header-cell {
            font-size: 18px;
            font-weight: 600;
            padding: 25px 20px;
            border-right: 1px solid rgba(255, 255, 255, 0.2);
        }

        .header-cell:last-child {
            border-right: none;
        }

        .table-row .table-cell {
            font-size: 16px;
            font-weight: 500;
            color: #ffffff;
        }

        .play-button {
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .play-button:hover {
            background: #1a1a1a;
            transform: scale(1.1);
        }

        .arrow-button {
            background: #000000;
            color: #ffffff;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .arrow-button:hover {
            background: #1a1a1a;
            transform: translateX(2px);
        }

        .view-community-button {
            background: linear-gradient(135deg, #000000 0%, #1a1a1a 100%);
            color: #ffffff;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-community-button:hover {
            transform: translateX(3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="header-left"></div>
            <div class="header-center">
                <h1>Homara</h1>
            </div>
            <button class="logout-button" id="logoutButton">Logout</button>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-container">
        <div class="tab-welcome">
            <span id="welcomeMessage">Welcome!</span>
        </div>
        <ul class="tab-nav">
            <li><button class="tab-button active" data-tab="home">HOME</button></li>
            <li><button class="tab-button" data-tab="payments">PAYMENTS</button></li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Home Tab -->
        <div id="home" class="tab-pane active">
            <div class="communities-section">
                <div class="communities-table">
                    <!-- Table Header -->
                    <div class="table-header">
                        <div class="table-cell header-cell">Your Communities</div>
                        <div class="table-cell header-cell">Role</div>
                        <div class="table-cell header-cell">Preview</div>
                        <div class="table-cell header-cell">Edit Your Point</div>
                        <div class="table-cell header-cell">View Community</div>
                    </div>

                    <!-- Table Rows -->
                    <div class="table-row">
                        <div class="table-cell">OregonTree</div>
                        <div class="table-cell">Founder</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                        <div class="founder-tab"></div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">AtxGuys</div>
                        <div class="table-cell">Member</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">NewYorkApple</div>
                        <div class="table-cell">Member</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">LAteam</div>
                        <div class="table-cell">Founder</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                        <div class="founder-tab"></div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">SeattleTower</div>
                        <div class="table-cell">Member</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">MiamiBeach</div>
                        <div class="table-cell">Founder</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                        <div class="founder-tab"></div>
                    </div>

                    <div class="table-row">
                        <div class="table-cell">PortlandForest</div>
                        <div class="table-cell">Member</div>
                        <div class="table-cell">
                            <button class="play-button">▶</button>
                        </div>
                        <div class="table-cell">
                            <button class="arrow-button">»»»</button>
                        </div>
                        <div class="table-cell">
                            <button class="view-community-button">→</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments Tab -->
        <div id="payments" class="tab-pane">
            <h2>Payments</h2>
            <p class="description">Manage your payments and billing information</p>
            
            <div class="card">
                <h3>Payment Options</h3>
                <button class="btn btn-primary">Add Payment Method</button>
                <button class="btn btn-success">View Payment History</button>
                <button class="btn btn-warning">Manage Billing</button>
            </div>

            <div class="card">
                <h3>Recent Transactions</h3>
                <p>No recent transactions to display.</p>
            </div>
        </div>


    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        let userData = null;

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Main window loaded');
            initializeTabs();
            initializeEventListeners();
            updateLoginTime();
        });

        // Listen for user data from main process
        ipcRenderer.on('user-data', (event, data) => {
            console.log('Received user data:', data);
            userData = data;
            updateUserInterface();
        });

        // Initialize tab functionality
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    
                    // Remove active class from all buttons and panes
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding pane
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // Initialize event listeners
        function initializeEventListeners() {
            // Logout button
            document.getElementById('logoutButton').addEventListener('click', () => {
                if (confirm('Are you sure you want to logout?')) {
                    ipcRenderer.send('logout');
                }
            });

            // Community button event listeners
            document.querySelectorAll('.play-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('Video preview coming soon!');
                });
            });

            document.querySelectorAll('.arrow-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('Edit your point feature coming soon!');
                });
            });

            document.querySelectorAll('.view-community-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('View community feature coming soon!');
                });
            });



            // Payment buttons
            document.querySelector('#payments .btn-primary').addEventListener('click', () => {
                alert('Add Payment Method feature coming soon!');
            });

            document.querySelector('#payments .btn-success').addEventListener('click', () => {
                alert('Payment History feature coming soon!');
            });

            document.querySelector('#payments .btn-warning').addEventListener('click', () => {
                alert('Billing Management feature coming soon!');
            });

            // Profile edit button
            document.querySelector('#profile .btn-primary').addEventListener('click', () => {
                alert('Profile editing feature coming soon!');
            });
        }

        // Update user interface with user data
        function updateUserInterface() {
            if (!userData) return;

            // Update header
            document.getElementById('welcomeMessage').textContent = `Welcome ${userData.username}!`;
            document.getElementById('userInfo').textContent = '';

            // Update home tab
            document.getElementById('userStat').textContent = `• Logged in as: ${userData.username}`;

            // Update profile tab (if they exist)
            const profileUsername = document.getElementById('profileUsername');
            const profileUserId = document.getElementById('profileUserId');
            const profileEmail = document.getElementById('profileEmail');

            if (profileUsername) profileUsername.textContent = `Username: ${userData.username}`;
            if (profileUserId) profileUserId.textContent = `User ID: ${userData.userId}`;
            if (profileEmail) profileEmail.textContent = `Email: ${userData.email || 'Not available'}`;
        }

        // Update login time
        function updateLoginTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('loginTime').textContent = timeString;
        }

        console.log('Main window script loaded');
    </script>
</body>
</html>
