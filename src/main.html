<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> App</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background-color: #f5f7fa;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }

        /* Header */
        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 20px 30px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        }

        .header-content {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .welcome-section h1 {
            font-size: 28px;
            font-weight: bold;
            margin-bottom: 5px;
        }

        .welcome-section .user-info {
            font-size: 14px;
            color: #bdc3c7;
        }

        .logout-button {
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 600;
            transition: background-color 0.3s ease;
        }

        .logout-button:hover {
            background-color: #c0392b;
        }

        /* Tab Navigation */
        .tab-container {
            background-color: white;
            border-bottom: 1px solid #e1e8ed;
        }

        .tab-nav {
            display: flex;
            justify-content: flex-end;
            list-style: none;
            margin: 0;
            padding: 0 30px;
        }

        .tab-nav li {
            margin-left: 20px;
        }

        .tab-nav button {
            padding: 15px 25px;
            background: none;
            border: none;
            cursor: pointer;
            font-size: 16px;
            font-weight: 600;
            color: #666;
            transition: all 0.3s ease;
            border-bottom: 3px solid transparent;
            text-transform: uppercase;
        }

        .tab-nav button:hover {
            background-color: #f8f9fa;
            color: #333;
        }

        .tab-nav button.active {
            color: #667eea;
            border-bottom-color: #667eea;
            background-color: #f8f9ff;
        }

        /* Tab Content */
        .tab-content {
            flex: 1;
            overflow-y: auto;
            padding: 30px;
        }

        .tab-pane {
            display: none;
            max-width: 1200px;
            margin: 0 auto;
        }

        .tab-pane.active {
            display: block;
        }

        .tab-pane h2 {
            font-size: 32px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #2c3e50;
        }

        .tab-pane .description {
            font-size: 18px;
            color: #666;
            margin-bottom: 30px;
        }

        /* Cards */
        .card {
            background: white;
            padding: 25px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }

        .card h3 {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #2c3e50;
        }

        .card p {
            color: #666;
            line-height: 1.6;
            margin-bottom: 10px;
        }

        /* Buttons */
        .btn {
            display: inline-block;
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 600;
            cursor: pointer;
            text-decoration: none;
            transition: all 0.3s ease;
            margin: 5px 10px 5px 0;
        }

        .btn-primary {
            background-color: #3498db;
            color: white;
        }

        .btn-primary:hover {
            background-color: #2980b9;
            transform: translateY(-2px);
        }

        .btn-success {
            background-color: #2ecc71;
            color: white;
        }

        .btn-success:hover {
            background-color: #27ae60;
            transform: translateY(-2px);
        }

        .btn-warning {
            background-color: #f39c12;
            color: white;
        }

        .btn-warning:hover {
            background-color: #e67e22;
            transform: translateY(-2px);
        }

        /* Stats */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            text-align: center;
        }

        .stat-card .stat-number {
            font-size: 32px;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 5px;
        }

        .stat-card .stat-label {
            color: #666;
            font-size: 14px;
        }

        /* Form Elements */
        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 500;
            color: #555;
        }

        .form-group input,
        .form-group select {
            width: 100%;
            padding: 12px 16px;
            border: 2px solid #e1e8ed;
            border-radius: 8px;
            font-size: 16px;
        }

        .form-group input:focus,
        .form-group select:focus {
            outline: none;
            border-color: #667eea;
        }

        .checkbox-group {
            display: flex;
            align-items: center;
            margin-bottom: 15px;
        }

        .checkbox-group input[type="checkbox"] {
            width: auto;
            margin-right: 10px;
        }

        /* Communities Section Styles */
        .communities-section {
            max-width: 1200px;
            margin: 0 auto;
            padding: 40px 20px;
        }

        .communities-section h2 {
            text-align: center;
            font-size: 32px;
            font-weight: 600;
            color: #333;
            margin-bottom: 40px;
        }

        .community-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .community-item {
            display: flex;
            align-items: center;
            background: white;
            border-radius: 12px;
            padding: 25px;
            box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            border: 1px solid #e1e5e9;
            transition: all 0.3s ease;
        }

        .community-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
        }

        .community-name {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            min-width: 200px;
            margin-right: 40px;
        }

        .community-actions {
            display: flex;
            align-items: center;
            gap: 30px;
            flex: 1;
        }

        .action-group {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 8px;
        }

        .action-label {
            font-size: 14px;
            font-weight: 500;
            color: #666;
            text-align: center;
        }

        .action-value {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            padding: 8px 16px;
            background: #f8f9fa;
            border-radius: 6px;
            border: 1px solid #e1e5e9;
        }

        .play-button {
            background: #667eea;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 18px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .play-button:hover {
            background: #5a67d8;
            transform: scale(1.1);
        }

        .arrow-button {
            background: #4a5568;
            color: white;
            border: none;
            border-radius: 6px;
            padding: 10px 16px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .arrow-button:hover {
            background: #2d3748;
            transform: translateX(2px);
        }

        .view-community-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            border-radius: 8px;
            padding: 12px 20px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .view-community-button:hover {
            transform: translateX(3px);
            box-shadow: 0 4px 12px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <!-- Header -->
    <div class="header">
        <div class="header-content">
            <div class="welcome-section">
                <h1 id="welcomeMessage">Welcome!</h1>
                <div class="user-info" id="userInfo">Loading...</div>
            </div>
            <button class="logout-button" id="logoutButton">Logout</button>
        </div>
    </div>

    <!-- Tab Navigation -->
    <div class="tab-container">
        <ul class="tab-nav">
            <li><button class="tab-button active" data-tab="home">HOME</button></li>
            <li><button class="tab-button" data-tab="payments">PAYMENTS</button></li>
        </ul>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
        <!-- Home Tab -->
        <div id="home" class="tab-pane active">
            <div class="communities-section">
                <h2>Your Communities</h2>

                <div class="community-list">
                    <!-- OregonTree Community -->
                    <div class="community-item">
                        <div class="community-name">OregonTree</div>
                        <div class="community-actions">
                            <div class="action-group">
                                <div class="action-label">Role</div>
                                <div class="action-value">Founder</div>
                            </div>
                            <div class="action-group">
                                <div class="action-label">Preview</div>
                                <button class="play-button">▶</button>
                            </div>
                            <div class="action-group">
                                <div class="action-label">Edit your point</div>
                                <button class="arrow-button">»»»</button>
                            </div>
                            <div class="action-group">
                                <button class="view-community-button">View Community →</button>
                            </div>
                        </div>
                    </div>

                    <!-- AtxGuys Community -->
                    <div class="community-item">
                        <div class="community-name">AtxGuys</div>
                        <div class="community-actions">
                            <div class="action-group">
                                <div class="action-label">Role</div>
                                <div class="action-value">Member</div>
                            </div>
                            <div class="action-group">
                                <div class="action-label">Preview</div>
                                <button class="play-button">▶</button>
                            </div>
                            <div class="action-group">
                                <div class="action-label">Edit your point</div>
                                <button class="arrow-button">»»»</button>
                            </div>
                            <div class="action-group">
                                <button class="view-community-button">View Community →</button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Payments Tab -->
        <div id="payments" class="tab-pane">
            <h2>Payments</h2>
            <p class="description">Manage your payments and billing information</p>
            
            <div class="card">
                <h3>Payment Options</h3>
                <button class="btn btn-primary">Add Payment Method</button>
                <button class="btn btn-success">View Payment History</button>
                <button class="btn btn-warning">Manage Billing</button>
            </div>

            <div class="card">
                <h3>Recent Transactions</h3>
                <p>No recent transactions to display.</p>
            </div>
        </div>


    </div>

    <script>
        const { ipcRenderer } = require('electron');
        
        let userData = null;

        // Initialize when DOM is loaded
        document.addEventListener('DOMContentLoaded', () => {
            console.log('Main window loaded');
            initializeTabs();
            initializeEventListeners();
            updateLoginTime();
        });

        // Listen for user data from main process
        ipcRenderer.on('user-data', (event, data) => {
            console.log('Received user data:', data);
            userData = data;
            updateUserInterface();
        });

        // Initialize tab functionality
        function initializeTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabPanes = document.querySelectorAll('.tab-pane');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    
                    // Remove active class from all buttons and panes
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabPanes.forEach(pane => pane.classList.remove('active'));
                    
                    // Add active class to clicked button and corresponding pane
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                });
            });
        }

        // Initialize event listeners
        function initializeEventListeners() {
            // Logout button
            document.getElementById('logoutButton').addEventListener('click', () => {
                if (confirm('Are you sure you want to logout?')) {
                    ipcRenderer.send('logout');
                }
            });

            // Community button event listeners
            document.querySelectorAll('.play-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('Video preview coming soon!');
                });
            });

            document.querySelectorAll('.arrow-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('Edit your point feature coming soon!');
                });
            });

            document.querySelectorAll('.view-community-button').forEach(button => {
                button.addEventListener('click', () => {
                    alert('View community feature coming soon!');
                });
            });



            // Payment buttons
            document.querySelector('#payments .btn-primary').addEventListener('click', () => {
                alert('Add Payment Method feature coming soon!');
            });

            document.querySelector('#payments .btn-success').addEventListener('click', () => {
                alert('Payment History feature coming soon!');
            });

            document.querySelector('#payments .btn-warning').addEventListener('click', () => {
                alert('Billing Management feature coming soon!');
            });

            // Profile edit button
            document.querySelector('#profile .btn-primary').addEventListener('click', () => {
                alert('Profile editing feature coming soon!');
            });
        }

        // Update user interface with user data
        function updateUserInterface() {
            if (!userData) return;

            // Update header
            document.getElementById('welcomeMessage').textContent = `Welcome, ${userData.username}!`;
            document.getElementById('userInfo').textContent = `User ID: ${userData.userId}`;

            // Update home tab
            document.getElementById('userStat').textContent = `• Logged in as: ${userData.username}`;

            // Update profile tab
            document.getElementById('profileUsername').textContent = `Username: ${userData.username}`;
            document.getElementById('profileUserId').textContent = `User ID: ${userData.userId}`;
            document.getElementById('profileEmail').textContent = `Email: ${userData.email || 'Not available'}`;
        }

        // Update login time
        function updateLoginTime() {
            const now = new Date();
            const timeString = now.toLocaleTimeString();
            document.getElementById('loginTime').textContent = timeString;
        }

        console.log('Main window script loaded');
    </script>
</body>
</html>
